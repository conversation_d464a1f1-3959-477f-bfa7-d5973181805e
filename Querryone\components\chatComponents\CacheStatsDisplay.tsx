import React, { useState, useEffect } from 'react';
import { CacheService, CacheStats } from './services/CacheService';

interface CacheStatsDisplayProps {
  className?: string;
  showDetails?: boolean;
}

const CacheStatsDisplay: React.FC<CacheStatsDisplayProps> = ({ 
  className = '', 
  showDetails = false 
}) => {
  const [stats, setStats] = useState<CacheStats>({
    totalQueries: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    cacheSize: 0
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateStats = () => {
      const currentStats = CacheService.getCacheStats();
      setStats(currentStats);
    };

    // Update stats initially
    updateStats();

    // Update stats every 5 seconds when visible
    let interval: NodeJS.Timeout | null = null;
    if (isVisible) {
      interval = setInterval(updateStats, 5000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isVisible]);

  const handleClearCache = () => {
    if (window.confirm('Are you sure you want to clear the cache? This will remove all cached responses.')) {
      CacheService.clearCache();
      setStats({
        totalQueries: 0,
        cacheHits: 0,
        cacheMisses: 0,
        hitRate: 0,
        cacheSize: 0
      });
    }
  };

  const getHitRateColor = (hitRate: number) => {
    if (hitRate >= 70) return 'text-green-600';
    if (hitRate >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!showDetails && !isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className={`text-xs text-gray-500 hover:text-gray-700 transition-colors ${className}`}
        title="Show cache statistics"
      >
        📊 Cache: {stats.cacheSize} items, {stats.hitRate}% hit rate
      </button>
    );
  }

  return (
    <div className={`bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">
          🚀 Cache Performance
        </h3>
        {!showDetails && (
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-gray-600 text-sm"
          >
            ✕
          </button>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Total Queries:</span>
            <span className="font-medium">{stats.totalQueries}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Cache Hits:</span>
            <span className="font-medium text-green-600">{stats.cacheHits}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Cache Misses:</span>
            <span className="font-medium text-red-600">{stats.cacheMisses}</span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Hit Rate:</span>
            <span className={`font-medium ${getHitRateColor(stats.hitRate)}`}>
              {stats.hitRate}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Cache Size:</span>
            <span className="font-medium">{stats.cacheSize} items</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Performance:</span>
            <span className="font-medium text-blue-600">
              {stats.totalQueries > 0 ? 
                `${Math.round((stats.cacheHits / stats.totalQueries) * 100)}% faster` : 
                'No data'
              }
            </span>
          </div>
        </div>
      </div>

      {stats.totalQueries > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="text-xs text-gray-500">
              💡 Cache responses include 2s delay for consistent UX
            </div>
            <button
              onClick={handleClearCache}
              className="text-xs text-red-500 hover:text-red-700 transition-colors"
              title="Clear all cached responses"
            >
              🗑️ Clear Cache
            </button>
          </div>
        </div>
      )}

      {stats.totalQueries === 0 && (
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 text-center">
            🔄 Start asking questions to see cache performance
          </div>
        </div>
      )}
    </div>
  );
};

export default CacheStatsDisplay;
