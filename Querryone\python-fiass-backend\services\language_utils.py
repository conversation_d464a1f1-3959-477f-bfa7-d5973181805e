"""
Language Detection and Multi-Language Support Utilities

This module provides language detection capabilities and utilities for 
multi-language content processing in the FAISS backend system.
"""

import re
from typing import Dict, List, Optional, Tuple
import pandas as pd

# Language detection patterns using Unicode ranges
LANGUAGE_PATTERNS = {
    'Tamil': re.compile(r'[\u0B80-\u0BFF]'),
    'Telugu': re.compile(r'[\u0C00-\u0C7F]'),
    'Kannada': re.compile(r'[\u0C80-\u0CFF]'),
    'Hindi': re.compile(r'[\u0900-\u097F]'),
    'Malayalam': re.compile(r'[\u0D00-\u0D7F]'),
    'Bengali': re.compile(r'[\u0980-\u09FF]'),
    'Gujarati': re.compile(r'[\u0A80-\u0AFF]'),
    'Punjabi': re.compile(r'[\u0A00-\u0A7F]'),
    'Marathi': re.compile(r'[\u0900-\u097F]'),  # Same as Hindi
    'Oriya': re.compile(r'[\u0B00-\u0B7F]'),
}

# Language to index mapping
LANGUAGE_INDEX_MAPPING = {
    'Tamil': 'default-tamil',
    'Telugu': 'default-telugu',
    'Kannada': 'default-kannada',
    'Hindi': 'default-hindi',
    'Malayalam': 'default-malayalam',
    'Bengali': 'default-bengali',
    'Gujarati': 'default-gujarati',
    'Punjabi': 'default-punjabi',
    'Marathi': 'default-marathi',
    'Oriya': 'default-oriya',
    'English': 'default',
    'default': 'default'
}

def detect_language_from_text(text: str, confidence_threshold: float = 0.1) -> str:
    """
    Detect language from text content using Unicode character patterns.
    
    Args:
        text: Input text to analyze
        confidence_threshold: Minimum ratio of language-specific characters needed
        
    Returns:
        str: Detected language name ('Tamil', 'Telugu', etc.) or 'English' as default
    """
    if not text or not isinstance(text, str):
        return 'English'
    
    text = text.strip()
    if not text:
        return 'English'
    
    # Count characters for each language
    language_scores = {}
    total_chars = len(text)
    
    for language, pattern in LANGUAGE_PATTERNS.items():
        matches = pattern.findall(text)
        if matches:
            score = len(''.join(matches)) / total_chars
            language_scores[language] = score
            print(f"🔍 Language detection - {language}: {score:.3f} ({len(''.join(matches))}/{total_chars} chars)")
    
    # Find language with highest score above threshold
    if language_scores:
        best_language = max(language_scores.items(), key=lambda x: x[1])
        if best_language[1] >= confidence_threshold:
            print(f"✅ Language detected: {best_language[0]} (confidence: {best_language[1]:.3f})")
            return best_language[0]
    
    print(f"🔤 No specific language detected, defaulting to English")
    return 'English'

def detect_language_from_dataframe(df: pd.DataFrame, sample_size: int = 50) -> str:
    """
    Detect language from pandas DataFrame content.
    
    Args:
        df: Pandas DataFrame to analyze
        sample_size: Number of text samples to analyze per column
        
    Returns:
        str: Detected language name
    """
    sample_texts = []
    
    # Collect text samples from string columns
    for column in df.columns:
        if df[column].dtype == 'object':  # String columns
            sample_values = df[column].dropna().head(sample_size)
            for value in sample_values:
                if isinstance(value, str) and len(value.strip()) > 0:
                    sample_texts.append(value)
    
    # Combine samples and detect language
    combined_text = ' '.join(sample_texts[:200])  # Limit to avoid memory issues
    return detect_language_from_text(combined_text)

def get_index_name_for_language(detected_language: str, user_selected_index: Optional[str] = None) -> str:
    """
    Get appropriate FAISS index name based on detected language and user preference.
    
    Args:
        detected_language: Language detected from content
        user_selected_index: User-specified index name (optional)
        
    Returns:
        str: FAISS index name to use
    """
    # If user explicitly selected an index, respect that choice
    if user_selected_index and user_selected_index != 'auto':
        print(f"🎯 Using user-selected index: {user_selected_index}")
        return user_selected_index
    
    # Map detected language to appropriate index
    index_name = LANGUAGE_INDEX_MAPPING.get(detected_language, 'default')
    print(f"🌏 Language '{detected_language}' mapped to index: {index_name}")
    return index_name

def is_language_text(text: str, language: str) -> bool:
    """
    Check if text contains characters from a specific language.
    
    Args:
        text: Text to check
        language: Language name to check for
        
    Returns:
        bool: True if text contains the specified language characters
    """
    if not text or not isinstance(text, str) or language not in LANGUAGE_PATTERNS:
        return False
    
    pattern = LANGUAGE_PATTERNS[language]
    return bool(pattern.search(text))

def get_supported_languages() -> List[str]:
    """
    Get list of supported languages for detection.
    
    Returns:
        List[str]: List of supported language names
    """
    return list(LANGUAGE_PATTERNS.keys()) + ['English']

def get_language_statistics(text: str) -> Dict[str, float]:
    """
    Get detailed statistics about language character distribution in text.
    
    Args:
        text: Text to analyze
        
    Returns:
        Dict[str, float]: Language names mapped to character ratio scores
    """
    if not text or not isinstance(text, str):
        return {}
    
    total_chars = len(text)
    if total_chars == 0:
        return {}
    
    stats = {}
    for language, pattern in LANGUAGE_PATTERNS.items():
        matches = pattern.findall(text)
        if matches:
            char_count = len(''.join(matches))
            ratio = char_count / total_chars
            stats[language] = ratio
    
    return stats
