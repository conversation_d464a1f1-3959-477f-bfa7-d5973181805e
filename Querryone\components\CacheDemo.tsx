'use client';

import React, { useState, useEffect } from 'react';
import { PiPlay, PiStop, PiArrowClockwise, PiClock, PiDatabase, PiSpeedometer } from 'react-icons/pi';
import CacheManager from './CacheManager';
import { CacheUtils, searchContent, processYouTubeURL, processArticleURL } from '../services/uploadService';
import toast from 'react-hot-toast';

const CacheDemo: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<Array<{
    operation: string;
    time: number;
    cached: boolean;
    timestamp: Date;
  }>>([]);

  // Demo operations
  const demoOperations = [
    {
      name: 'Search Query 1',
      operation: () => searchContent('financial news', { k: 5, index_name: 'default' }),
      type: 'search'
    },
    {
      name: 'Search Query 2',
      operation: () => searchContent('market analysis', { k: 5, index_name: 'default' }),
      type: 'search'
    },
    {
      name: 'YouTube URL',
      operation: () => processYouTubeURL('https://www.youtube.com/watch?v=dQw4w9WgXcQ'),
      type: 'upload'
    },
    {
      name: 'Article URL',
      operation: () => processArticleURL('https://example.com/article'),
      type: 'upload'
    }
  ];

  const runDemo = async () => {
    setIsRunning(true);
    setResults([]);
    
    try {
      for (const demo of demoOperations) {
        if (!isRunning) break;
        
        // First run (should be slow)
        const startTime1 = Date.now();
        const result1 = await demo.operation();
        const time1 = Date.now() - startTime1;
        
        setResults(prev => [...prev, {
          operation: `${demo.name} (First)`,
          time: time1,
          cached: false,
          timestamp: new Date()
        }]);
        
        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Second run (should be fast from cache)
        const startTime2 = Date.now();
        const result2 = await demo.operation();
        const time2 = Date.now() - startTime2;
        
        setResults(prev => [...prev, {
          operation: `${demo.name} (Cached)`,
          time: time2,
          cached: result2.cached || false,
          timestamp: new Date()
        }]);
        
        // Wait before next operation
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      toast.success('Cache demo completed!');
    } catch (error) {
      console.error('Demo error:', error);
      toast.error('Demo failed - backend may not be available');
    } finally {
      setIsRunning(false);
    }
  };

  const stopDemo = () => {
    setIsRunning(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const getSpeedImprovement = (originalTime: number, cachedTime: number) => {
    if (cachedTime === 0) return '∞';
    const improvement = originalTime / cachedTime;
    return `${improvement.toFixed(1)}x`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-6">
        <h1 className="text-2xl font-bold mb-2">Cache Memory Demo</h1>
        <p className="text-blue-100">
          Demonstration of cache functionality for faster response times. 
          This demo shows the performance difference between fresh API calls and cached responses.
        </p>
      </div>

      {/* Cache Manager */}
      <CacheManager />

      {/* Demo Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Performance Demo
          </h2>
          <div className="flex gap-2">
            <button
              onClick={runDemo}
              disabled={isRunning}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <PiPlay className="w-4 h-4" />
              {isRunning ? 'Running...' : 'Start Demo'}
            </button>
            <button
              onClick={stopDemo}
              disabled={!isRunning}
              className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <PiStop className="w-4 h-4" />
              Stop
            </button>
            <button
              onClick={clearResults}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <PiArrowClockwise className="w-4 h-4" />
              Clear
            </button>
          </div>
        </div>

        {/* Demo Description */}
        <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
            How the Demo Works:
          </h3>
          <ul className="text-xs text-blue-700 dark:text-blue-400 space-y-1">
            <li>• Each operation runs twice - first without cache, then with cache</li>
            <li>• First run processes the request normally (slower)</li>
            <li>• Second run uses cached result (much faster)</li>
            <li>• Compare response times to see cache performance benefits</li>
          </ul>
        </div>

        {/* Results */}
        {results.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Demo Results
            </h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    result.cached
                      ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700'
                      : 'bg-gray-50 border-gray-200 dark:bg-gray-700 dark:border-gray-600'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-1 rounded ${
                      result.cached ? 'bg-green-500' : 'bg-gray-500'
                    }`}>
                      {result.cached ? (
                        <PiDatabase className="w-4 h-4 text-white" />
                      ) : (
                        <PiSpeedometer className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {result.operation}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {result.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`flex items-center gap-1 text-sm font-medium ${
                      result.cached ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      <PiClock className="w-4 h-4" />
                      {formatTime(result.time)}
                    </div>
                    {result.cached && (
                      <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full dark:bg-green-900/30 dark:text-green-400">
                        Cached
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Performance Summary */}
            {results.length >= 2 && (
              <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-2">
                  Performance Summary:
                </h4>
                <div className="text-xs text-yellow-700 dark:text-yellow-400 space-y-2">
                  {results.filter(r => r.cached).length > 0 && (
                    <>
                      <p>
                        Cache provides significant performance improvements, with responses
                        typically 10-100x faster than fresh API calls.
                      </p>
                      <div className="grid grid-cols-2 gap-4 mt-2">
                        <div>
                          <span className="font-medium">Average Fresh Response:</span>
                          <span className="ml-2">
                            {formatTime(
                              Math.round(
                                results.filter(r => !r.cached).reduce((sum, r) => sum + r.time, 0) /
                                Math.max(results.filter(r => !r.cached).length, 1)
                              )
                            )}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium">Average Cached Response:</span>
                          <span className="ml-2">
                            {formatTime(
                              Math.round(
                                results.filter(r => r.cached).reduce((sum, r) => sum + r.time, 0) /
                                Math.max(results.filter(r => r.cached).length, 1)
                              )
                            )}
                          </span>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Loading State */}
        {isRunning && (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-3 text-blue-600 dark:text-blue-400">
              <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span>Running cache performance demo...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CacheDemo;
