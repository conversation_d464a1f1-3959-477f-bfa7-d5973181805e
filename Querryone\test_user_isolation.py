#!/usr/bin/env python3
"""
Test script to verify user-specific FAISS index filtering functionality.
This script tests that users can only see and access their own FAISS indexes.
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:5010"
TEST_USERS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"  # User who actually has access to 'financial' index
]

def test_list_indexes_without_user():
    """Test listing all indexes without user filtering (GET request)"""
    print("\n🔍 Testing: List all indexes without user filtering")
    
    try:
        response = requests.get(f"{BASE_URL}/api/list-faiss-indexes")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ GET request successful")
            print(f"   - Success: {data.get('success')}")
            print(f"   - Total indexes: {data.get('total_count', 0)}")
            print(f"   - Indexes: {data.get('indexes', [])}")
            print(f"   - Filtered by user: {data.get('filtered_by_user', False)}")
            return data.get('indexes', [])
        else:
            print(f"❌ GET request failed: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error in GET request: {e}")
        return []

def test_list_indexes_with_user(user_email):
    """Test listing indexes filtered by user email (POST request)"""
    print(f"\n🔍 Testing: List indexes for user {user_email}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/list-faiss-indexes",
            json={"email": user_email},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ POST request successful for {user_email}")
            print(f"   - Success: {data.get('success')}")
            print(f"   - Total indexes: {data.get('total_count', 0)}")
            print(f"   - Indexes: {data.get('indexes', [])}")
            print(f"   - Filtered by user: {data.get('filtered_by_user', False)}")
            print(f"   - User email: {data.get('user_email')}")
            return data.get('indexes', [])
        else:
            print(f"❌ POST request failed for {user_email}: {response.status_code}")
            print(f"   Response: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Error in POST request for {user_email}: {e}")
        return []

def test_user_access_validation(user_email, index_name):
    """Test user access validation for a specific index"""
    print(f"\n🔍 Testing: Access validation for {user_email} to index '{index_name}'")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/validate-user-access",
            json={"email": user_email, "index_name": index_name},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            has_access = data.get('has_access', False)
            message = data.get('message', '')
            
            print(f"✅ Access validation successful")
            print(f"   - User: {user_email}")
            print(f"   - Index: {index_name}")
            print(f"   - Has access: {has_access}")
            print(f"   - Message: {message}")
            return has_access
        else:
            print(f"❌ Access validation failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error in access validation: {e}")
        return False

def test_query_with_access_control(user_email, index_name, query="test query"):
    """Test querying an index with user access control"""
    print(f"\n🔍 Testing: Query access for {user_email} to index '{index_name}'")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/query-faiss",
            json={
                "query": query,
                "index_name": index_name,
                "user_email": user_email,
                "k": 3
            },
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Query successful for {user_email}")
            print(f"   - Index: {index_name}")
            print(f"   - Results found: {len(data.get('data', {}).get('results', []))}")
            return True
        elif response.status_code == 403:
            data = response.json()
            print(f"🚫 Query access denied for {user_email}")
            print(f"   - Index: {index_name}")
            print(f"   - Error: {data.get('error', 'Access denied')}")
            return False
        else:
            print(f"❌ Query failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error in query test: {e}")
        return False

def test_debug_pine_collection():
    """Test the debug endpoint to see what's in the pine_collection"""
    print(f"\n🔍 Testing: Debug PINE collection contents")
    
    try:
        response = requests.get(f"{BASE_URL}/api/debug-pine-collection")
        
        if response.status_code == 200:
            data = response.json()
            records = data.get('records', [])
            print(f"✅ Debug successful")
            print(f"   - Total records: {data.get('total_records', 0)}")
            
            if records:
                print("   - Records:")
                for record in records[:10]:  # Show first 10 records
                    print(f"     * ID: {record.get('id')}, Index: {record.get('index_name')}, Email: {record.get('email')}")
                if len(records) > 10:
                    print(f"     ... and {len(records) - 10} more records")
            else:
                print("   - No records found")
            return records
        else:
            print(f"❌ Debug failed: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error in debug test: {e}")
        return []

def main():
    """Run all tests"""
    print("🚀 Starting User Isolation Tests for FAISS Index Filtering")
    print("=" * 60)

    # Test 1: List all indexes without filtering
    all_indexes = test_list_indexes_without_user()

    # Test 2: Debug PINE collection to see what data exists
    pine_records = test_debug_pine_collection()

    # Test 3: List indexes for each test user
    user_indexes = {}
    for user in TEST_USERS:
        user_indexes[user] = test_list_indexes_with_user(user)

    # Test 4: Test access validation for each user to each index
    if all_indexes:
        for user in TEST_USERS:
            for index in all_indexes:
                test_user_access_validation(user, index)

    # Test 5: Test query access control
    if all_indexes:
        for user in TEST_USERS:
            for index in all_indexes[:2]:  # Test first 2 indexes only
                test_query_with_access_control(user, index)

    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"   - Total indexes found: {len(all_indexes)}")
    print(f"   - PINE collection records: {len(pine_records)}")

    for user, indexes in user_indexes.items():
        print(f"   - {user}: {len(indexes)} accessible indexes")
        if 'default' in indexes:
            print(f"     ✅ {user} has access to default index (correct for all users)")
        else:
            print(f"     ❌ {user} does NOT have access to default index (should have access)")

    # Test default index access specifically
    print("\n🎯 Default Index Access Test:")
    for user in TEST_USERS:
        user_indexes_list = user_indexes.get(user, [])
        if 'default' in user_indexes_list:
            print(f"   ✅ {user}: Can access default index")
        else:
            print(f"   ❌ {user}: Cannot access default index (this should be fixed)")

    print("\n✅ User isolation tests completed!")
    print("📝 Expected behavior:")
    print("   - All users should have access to 'default' index")
    print("   - Users should only see their own custom indexes + default")
    print("   - Users should be able to query default index successfully")

if __name__ == "__main__":
    main()
