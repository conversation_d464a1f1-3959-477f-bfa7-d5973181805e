import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>inkS<PERSON>ple,
  <PERSON><PERSON>heck,
  PiCopy,
  PiFacebookLogo,
  PiTwitterLogo,
  PiLinkedinLogo,
  PiWhatsappLogo,
  PiShareFat,
  PiGlobe,
  PiLock
} from "react-icons/pi";
import { usePathname } from "next/navigation";
import { useChatHandler } from "@/stores/chatList";
import { sharingService, ShareSettings } from "@/services/sharingService";

function ShareLinkModal() {
  const [shareLink, setShareLink] = useState<string>("");
  const [isLinkGenerated, setIsLinkGenerated] = useState<boolean>(false);
  const [isCopied, setIsCopied] = useState<boolean>(false);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [shareSettings, setShareSettings] = useState({
    includeMessages: true,
    publicAccess: true,
    expiresIn: "never" // "1day", "7days", "30days", "never"
  });

  const pathname = usePathname();
  const { chatList } = useChatHandler();

  // Extract chat ID from pathname
  const chatId = pathname?.split("/chat/")[1];
  const currentChat = chatList.find(chat => chat.id === chatId);

  useEffect(() => {
    if (chatId && !isLinkGenerated) {
      generateShareLink();
    }
  }, [chatId]);

  // Regenerate link when settings change
  useEffect(() => {
    if (isLinkGenerated) {
      setIsLinkGenerated(false);
      setShareLink("");
      generateShareLink();
    }
  }, [shareSettings.includeMessages, shareSettings.publicAccess, shareSettings.expiresIn]);

  const generateShareLink = async () => {
    if (!chatId || !currentChat) return;

    setIsGenerating(true);

    try {
      // Create shareable chat using the sharing service
      const { shareUrl } = await sharingService.createShareableChat(
        currentChat,
        shareSettings as ShareSettings
      );

      setShareLink(shareUrl);
      setIsLinkGenerated(true);
    } catch (error) {
      console.error("Failed to generate share link:", error);
      // You could show an error message to the user here
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyLink = async () => {
    if (!shareLink) return;

    try {
      await navigator.clipboard.writeText(shareLink);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy link:", error);
    }
  };

  const handleSocialShare = (platform: string) => {
    if (!shareLink || !currentChat) return;

    const metadata = sharingService.generateShareMetadata(currentChat);
    const title = metadata.title;
    const text = metadata.description;

    const urls = {
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareLink)}&quote=${encodeURIComponent(text)}`,
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareLink)}&text=${encodeURIComponent(text)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareLink)}&title=${encodeURIComponent(title)}&summary=${encodeURIComponent(text)}`,
      whatsapp: `https://wa.me/?text=${encodeURIComponent(`${text} ${shareLink}`)}`
    };

    if (urls[platform as keyof typeof urls]) {
      window.open(urls[platform as keyof typeof urls], '_blank', 'width=600,height=400');
    }
  };

  const handleNativeShare = async () => {
    if (!shareLink || !currentChat) return;

    if (navigator.share) {
      try {
        const metadata = sharingService.generateShareMetadata(currentChat);
        await navigator.share({
          title: metadata.title,
          text: metadata.description,
          url: shareLink,
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    } else {
      handleCopyLink();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Share Conversation</h3>
        <p className="text-sm text-n400">
          Your name, custom instructions, and any messages you add after sharing
          stay private.{" "}
          {/* <span className="font-medium text-primaryColor cursor-pointer hover:underline">
            Learn more
          </span> */}
        </p>
      </div>

      {/* Share Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Share Settings</h4>

        <div className="space-y-3">
          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={shareSettings.includeMessages}
              onChange={(e) => setShareSettings(prev => ({ ...prev, includeMessages: e.target.checked }))}
              className="w-4 h-4 text-primaryColor border-gray-300 rounded focus:ring-primaryColor"
            />
            <span className="text-sm">Include all messages in conversation</span>
          </label>

          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={shareSettings.publicAccess}
              onChange={(e) => setShareSettings(prev => ({ ...prev, publicAccess: e.target.checked }))}
              className="w-4 h-4 text-primaryColor border-gray-300 rounded focus:ring-primaryColor"
            />
            <div className="flex items-center gap-2">
              {shareSettings.publicAccess ? <PiGlobe className="text-sm" /> : <PiLock className="text-sm" />}
              <span className="text-sm">
                {shareSettings.publicAccess ? "Public access" : "Private access"}
              </span>
            </div>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Link expires</label>
          <select
            value={shareSettings.expiresIn}
            onChange={(e) => setShareSettings(prev => ({ ...prev, expiresIn: e.target.value }))}
            className="w-full px-3 py-2 border border-primaryColor/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primaryColor/20"
          >
            <option value="never">Never</option>
            <option value="1day">1 day</option>
            <option value="7days">7 days</option>
            <option value="30days">30 days</option>
          </select>
        </div>
      </div>

      {/* Share Link Section */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Share Link</h4>

        {isGenerating ? (
          <div className="border border-primaryColor/20 rounded-xl py-4 px-5 flex items-center justify-center">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primaryColor"></div>
              <span className="text-sm text-n400">Generating share link...</span>
            </div>
          </div>
        ) : shareLink ? (
          <div className="border border-primaryColor/20 rounded-xl py-3 px-4 flex justify-between items-center">
            <div className="flex-1 mr-3">
              <p className="text-n100 text-sm truncate">{shareLink}</p>
            </div>
            <button
              onClick={handleCopyLink}
              className="flex justify-center items-center gap-2 text-white px-4 py-2 rounded-lg bg-primaryColor border border-primaryColor hover:bg-primaryColor/90 transition-colors"
            >
              {isCopied ? (
                <>
                  <PiCheck className="text-lg" />
                  <span className="text-sm font-semibold">Copied!</span>
                </>
              ) : (
                <>
                  <PiCopy className="text-lg" />
                  <span className="text-sm font-semibold">Copy Link</span>
                </>
              )}
            </button>
          </div>
        ) : (
          <div className="border border-primaryColor/20 rounded-xl py-4 px-5 flex items-center justify-center">
            <button
              onClick={generateShareLink}
              className="flex justify-center items-center gap-2 text-primaryColor hover:text-primaryColor/80 transition-colors"
            >
              <PiLinkSimple className="text-lg" />
              <span className="text-sm font-semibold">Generate Share Link</span>
            </button>
          </div>
        )}
      </div>

      {/* Social Sharing */}
      {shareLink && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Share on Social Media</h4>

          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => handleSocialShare('facebook')}
              className="flex items-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors"
            >
              <PiFacebookLogo className="text-xl text-blue-600" />
              <span className="text-sm font-medium">Facebook</span>
            </button>

            <button
              onClick={() => handleSocialShare('twitter')}
              className="flex items-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors"
            >
              <PiTwitterLogo className="text-xl text-blue-400" />
              <span className="text-sm font-medium">Twitter</span>
            </button>

            <button
              onClick={() => handleSocialShare('linkedin')}
              className="flex items-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors"
            >
              <PiLinkedinLogo className="text-xl text-blue-700" />
              <span className="text-sm font-medium">LinkedIn</span>
            </button>

            <button
              onClick={() => handleSocialShare('whatsapp')}
              className="flex items-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors"
            >
              <PiWhatsappLogo className="text-xl text-green-500" />
              <span className="text-sm font-medium">WhatsApp</span>
            </button>
          </div>
        </div>
      )}

      {/* Native Share (Mobile) */}
      {shareLink && typeof navigator !== 'undefined' && 'share' in navigator && (
        <div className="space-y-4">
          <button
            onClick={handleNativeShare}
            className="w-full flex items-center justify-center gap-3 p-3 border border-primaryColor/20 rounded-lg hover:bg-primaryColor/5 transition-colors"
          >
            <PiShareFat className="text-xl text-primaryColor" />
            <span className="text-sm font-medium">Share via Device</span>
          </button>
        </div>
      )}

      {/* Chat Info */}
      {currentChat && (
        <div className="bg-n50 dark:bg-n800 rounded-lg p-4 space-y-2">
          <h5 className="text-sm font-medium">Conversation Preview</h5>
          <p className="text-sm text-n400 truncate">{currentChat.title}</p>
          <p className="text-xs text-n300">
            {currentChat.messages.length} message{currentChat.messages.length !== 1 ? 's' : ''} •
            Created {new Date(currentChat.createdAt).toLocaleDateString()}
          </p>
        </div>
      )}
    </div>
  );
}

export default ShareLinkModal;
