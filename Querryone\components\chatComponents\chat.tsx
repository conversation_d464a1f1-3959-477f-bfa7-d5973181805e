import React, { useEffect, useRef, useState } from "react";
import { useChatHandler } from "@/stores/chatList";
import UserMessage from "@/components/chatComponents/UserMessage";
import BotReply from "@/components/chatComponents/BotReply";
import ChatBox from "@/components/chatComponents/ChatBox";

interface ChatProps {
  chatId: string;
}

function Chat({ chatId }: ChatProps) {
  const [scroll, setScroll] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("English");
  const { chatList, updateChatList, isAnimation } = useChatHandler();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    updateChatList();
  }, [updateChatList]);

  // Get current chat data
  const currentChat = chatList.find((chat) => chat.id === chatId);

  // Scroll to bottom when messages are added
  useEffect(() => {
    if (containerRef.current && scroll) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [scroll, chatList, currentChat]);

  if (!currentChat) {
    return <div>Chat not found</div>;
  }

  return (
    <div className="flex flex-col justify-between h-full pt-4 pb-5">
      <div
        ref={containerRef}
        className="flex-1 overflow-y-auto flex flex-col justify-start items-start gap-10 px-4 sm:px-6"
      >
        {currentChat.messages.map((message, index) => {
          if (message.isUser) {
            return (
              <UserMessage
                key={index}
                message={message.text as string}
                timestamp={message.timestamp}
                uploadedFiles={message.uploadedFiles}
                uploadedURLs={message.uploadedURLs}
                selectedLanguage={selectedLanguage}
              />
            );
          } else {
            // This is a bot message - render the BotReply component with the corresponding API response
            return (
              <BotReply
                key={index}
                replyType="response"
                setScroll={setScroll}
                isAnimation={isAnimation}
                aiResponse={message.text}
                timestamp={message.timestamp}
                messageId={`msg-${message.timestamp}-${index}`}
                selectedLanguage={selectedLanguage}
              />
            );
          }
        })}
      </div>
      <ChatBox onLanguageChange={setSelectedLanguage} />
    </div>
  );
}

export default Chat;