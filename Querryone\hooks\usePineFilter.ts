/**
 * React Hook for PINE Collection Email-Based Filtering
 * 
 * This hook provides easy integration of email-based filtering functionality
 * for React components that need to work with PINE collection data.
 */

import { useState, useEffect, useCallback } from 'react';
import { PineFilterService, UserIndicesResponse, UserAccessResponse } from '../services/pineFilterService';

// Types
interface UsePineFilterState {
  userIndices: string[];
  isLoading: boolean;
  error: string | null;
  hasAccess: boolean;
  userEmail: string | null;
}

interface UsePineFilterReturn extends UsePineFilterState {
  refreshIndices: () => Promise<void>;
  validateAccess: (indexName: string) => Promise<boolean>;
  filterIndices: (indices: string[]) => Promise<string[]>;
  getSafeIndex: () => Promise<string>;
  checkHasIndices: () => Promise<boolean>;
}

/**
 * Hook for managing PINE collection email-based filtering
 */
export const usePineFilter = (userEmail?: string): UsePineFilterReturn => {
  const [state, setState] = useState<UsePineFilterState>({
    userIndices: [],
    isLoading: true,
    error: null,
    hasAccess: false,
    userEmail: null
  });

  // Get current user email
  const getCurrentUserEmail = useCallback((): string | null => {
    if (userEmail) return userEmail;
    
    try {
      if (typeof window === 'undefined') return null;
      
      // Try multiple sources for user email
      const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');
      if (directEmail) return directEmail;
      
      // Try from user session data
      const userSession = sessionStorage.getItem('resultUser');
      if (userSession) {
        const userData = JSON.parse(userSession);
        return userData.email || userData.username || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting current user email:', error);
      return null;
    }
  }, [userEmail]);

  // Refresh user indices
  const refreshIndices = useCallback(async (): Promise<void> => {
    const email = getCurrentUserEmail();
    
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      if (!email) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'No user email found. Please log in again.',
          userIndices: [],
          hasAccess: false,
          userEmail: null
        }));
        return;
      }

      const response: UserIndicesResponse = await PineFilterService.getUserIndices(email);
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: null,
          userIndices: response.indices,
          hasAccess: response.indices.length > 0,
          userEmail: email
        }));
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: response.error || response.message,
          userIndices: [],
          hasAccess: false,
          userEmail: email
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        userIndices: [],
        hasAccess: false,
        userEmail: email
      }));
    }
  }, [getCurrentUserEmail]);

  // Validate access to a specific index
  const validateAccess = useCallback(async (indexName: string): Promise<boolean> => {
    try {
      const email = getCurrentUserEmail();
      if (!email) return false;

      const response: UserAccessResponse = await PineFilterService.validateUserAccess(indexName, email);
      return response.success && response.has_access;
    } catch (error) {
      console.error('Error validating access:', error);
      return false;
    }
  }, [getCurrentUserEmail]);

  // Filter indices to only include user's accessible ones
  const filterIndices = useCallback(async (indices: string[]): Promise<string[]> => {
    try {
      const email = getCurrentUserEmail();
      if (!email) return [];

      return await PineFilterService.filterIndicesByUser(indices, email);
    } catch (error) {
      console.error('Error filtering indices:', error);
      return [];
    }
  }, [getCurrentUserEmail]);

  // Get a safe index for the user
  const getSafeIndex = useCallback(async (): Promise<string> => {
    try {
      const email = getCurrentUserEmail();
      return await PineFilterService.getSafeIndexForUser(email || undefined);
    } catch (error) {
      console.error('Error getting safe index:', error);
      return 'default';
    }
  }, [getCurrentUserEmail]);

  // Check if user has any indices
  const checkHasIndices = useCallback(async (): Promise<boolean> => {
    try {
      const email = getCurrentUserEmail();
      if (!email) return false;

      return await PineFilterService.hasUserIndices(email);
    } catch (error) {
      console.error('Error checking if user has indices:', error);
      return false;
    }
  }, [getCurrentUserEmail]);

  // Initialize on mount and when userEmail changes
  useEffect(() => {
    refreshIndices();
  }, [refreshIndices]);

  return {
    ...state,
    refreshIndices,
    validateAccess,
    filterIndices,
    getSafeIndex,
    checkHasIndices
  };
};

/**
 * Hook for validating access to a specific index
 */
export const usePineIndexAccess = (indexName: string, userEmail?: string) => {
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [isValidating, setIsValidating] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const validateAccess = useCallback(async () => {
    if (!indexName) {
      setHasAccess(false);
      setIsValidating(false);
      setError('Index name is required');
      return;
    }

    setIsValidating(true);
    setError(null);

    try {
      const response = await PineFilterService.validateUserAccess(indexName, userEmail);
      setHasAccess(response.success && response.has_access);
      
      if (!response.success) {
        setError(response.error || response.message);
      }
    } catch (error) {
      setHasAccess(false);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsValidating(false);
    }
  }, [indexName, userEmail]);

  useEffect(() => {
    validateAccess();
  }, [validateAccess]);

  return {
    hasAccess,
    isValidating,
    error,
    revalidate: validateAccess
  };
};

/**
 * Hook for getting filtered indices
 */
export const useFilteredIndices = (allIndices: string[], userEmail?: string) => {
  const [filteredIndices, setFilteredIndices] = useState<string[]>([]);
  const [isFiltering, setIsFiltering] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const filterIndices = useCallback(async () => {
    if (!allIndices || allIndices.length === 0) {
      setFilteredIndices([]);
      setIsFiltering(false);
      return;
    }

    setIsFiltering(true);
    setError(null);

    try {
      const filtered = await PineFilterService.filterIndicesByUser(allIndices, userEmail);
      setFilteredIndices(filtered);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
      setFilteredIndices([]);
    } finally {
      setIsFiltering(false);
    }
  }, [allIndices, userEmail]);

  useEffect(() => {
    filterIndices();
  }, [filterIndices]);

  return {
    filteredIndices,
    isFiltering,
    error,
    refilter: filterIndices
  };
};
