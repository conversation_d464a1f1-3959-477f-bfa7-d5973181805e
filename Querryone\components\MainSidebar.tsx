import Image from "next/image";
import React, { useEffect, useState } from "react";
import logoLight from "@/public/images/logo5.png";
import logoDark from "@/public/images/logo6.png";
import { baseUrl, uid } from "@/components/api/api";
import { useTheme } from "next-themes";
import {
  PiAlignLeft,
  PiArchive,
  PiArrowUUpLeft,
  PiCaretDown,
  PiCaretUp,
  PiChatTeardropText,
  PiDeviceMobileCamera,
  PiDiamondsFour,
  PiDotsThreeBold,
  PiGear,
  PiMagnifyingGlass,
  PiPaintBucket,
  PiPencilLine,
  PiQuestion,
  PiRobot,
  PiShareFat,
  PiTrash,
} from "react-icons/pi";
import Link from "next/link";
import { useMainModal } from "@/stores/modal";
import { useChatHandler } from "@/stores/chatList";

type MainSidebarProps = {
  showSidebar: boolean;
  setShowSidebar: React.Dispatch<React.SetStateAction<boolean>>;
};


const deleteChatById = async (chatIdToDelete: string, updateChatList: () => void) => {
  if (typeof window === 'undefined') return;

  const resultUser = JSON.parse(sessionStorage.getItem("resultUser") || '{}');
  const userId = resultUser._id?.$oid;


  if (!userId || !uid) return;

  try {
    const fetchRes = await fetch(`${baseUrl}/eSearch?userId=${userId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "xxxid": uid,
      },
    });

    const fetchData = await fetchRes.json();

    if (Array.isArray(fetchData?.source)) {
      for (const item of fetchData.source) {
        try {
          const parsed = JSON.parse(item);
          const resourceId = parsed?._id?.$oid;
          const chats = parsed?.chats;

          if (resourceId && Array.isArray(chats)) {
            const chatExists = chats.some((chat) => chat.id === chatIdToDelete);

            if (chatExists) {
              const updatedChats = chats.filter(chat => chat.id !== chatIdToDelete);

              const updatedResource = {
                ...parsed,
                chats: updatedChats,
              };

              const updateRes = await fetch(`${baseUrl}/eUpdate?resourceId=${resourceId}&userId=${userId}`, {
                method: "PUT",
                headers: {
                  "Content-Type": "application/json",
                  "xxxid": uid,
                },
                body: JSON.stringify(updatedResource),
              });

              if (!updateRes.ok) {
                console.error("Failed to update chat list.");
              } else {
                console.log("Chat deleted successfully.");
                updateChatList(); // ✅ Refresh local chat list
              }
              return;
            }
          }
        } catch (err) {
          console.warn("Skipping invalid resource item", item);
        }
      }
    }
  } catch (error) {
    console.error("Error deleting chat:", error);
  }
};


// function to  rename

const renameChatById = async (
  chatIdToRename: string,
  newTitle: string,
  updateChatList: () => void
) => {
  if (typeof window === 'undefined') return;

  const resultUser = JSON.parse(sessionStorage.getItem("resultUser") || '{}');
  const userId = resultUser._id?.$oid;
  if (!userId || !uid) return;

  try {
    const fetchRes = await fetch(`${baseUrl}/eSearch?userId=${userId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "xxxid": uid,
      },
    });

    const fetchData = await fetchRes.json();
    if (Array.isArray(fetchData?.source)) {
      for (const item of fetchData.source) {
        try {
          const parsed = JSON.parse(item);
          const resourceId = parsed?._id?.$oid;
          const chats = parsed?.chats;

          if (resourceId && Array.isArray(chats)) {
            const chatIndex = chats.findIndex((chat) => chat.id === chatIdToRename);
            if (chatIndex !== -1) {
              chats[chatIndex].title = newTitle;
              const updatedResource = { ...parsed, chats };

              const updateRes = await fetch(
                `${baseUrl}/eUpdate?resourceId=${resourceId}&userId=${userId}`,
                {
                  method: "PUT",
                  headers: {
                    "Content-Type": "application/json",
                    "xxxid": uid,
                  },
                  body: JSON.stringify(updatedResource),
                }
              );

              if (updateRes.ok) {
                console.log("Chat renamed successfully.");
                updateChatList();
              }
              return;
            }
          }
        } catch (err) {
          console.warn("Skipping invalid resource item", item);
        }
      }
    }
  } catch (error) {
    console.error("Error renaming chat:", error);
  }
};



function MainSidebar({ showSidebar, setShowSidebar }: MainSidebarProps) {
  const [showMoreButton, setShowMoreButton] = useState(NaN);
  const { modalOpen } = useMainModal();

  // const { chatList } = useChatHandler();
  // COMMENTED THE ABOVE LINE AND ADDED THIS BELOW TWO LINE
  const chatList = useChatHandler((state) => state.chatList);
  const updateChatList = useChatHandler((state) => state.updateChatList);

  // -----------------------------------------------------
  const [showAllRecentChats, setShowAllRecentChats] = useState(false);
  const [currentLogo, setCurrentLogo] = useState(logoLight);
  const { resolvedTheme } = useTheme();


  // 👇 Rename states
  const [renamingChatId, setRenamingChatId] = useState<string | null>(null);
  const [newTitle, setNewTitle] = useState<string>("");


  // delete states
  const [chatIdToDelete, setChatIdToDelete] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const chatToDelete = chatList.find((chat) => chat.id === chatIdToDelete);

  // Load chats on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      updateChatList(); // this is what loads the chats
    }
  }, [updateChatList]);

  // Update logo based on theme
  useEffect(() => {
    setCurrentLogo(resolvedTheme === 'dark' ? logoDark : logoLight);
  }, [resolvedTheme]);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.innerWidth > 992) {
      setShowSidebar(true);
    }
  }, [setShowSidebar]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      // Check if the click is outside the dropdown menu
      if (!target.closest('.dropdown-container')) {
        setShowMoreButton(NaN);
      }
    };

    if (showMoreButton !== NaN) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMoreButton]);

  return (
    <>
    <div
      className={`w-[312px] bg-white dark:bg-n0 border-r border-primaryColor/20  h-dvh overflow-hidden duration-500 max-lg:absolute  z-40  top-0  left-0   ${showSidebar
        ? "  visible opacity-100 "
        : "max-lg:invisible max-lg:opacity-0 ml-[-312px]"
        }`}
    >
      <div
        className={` p-5 bg-primaryColor/5  overflow-auto h-full flex flex-col justify-between `}
      >
        <div className="">
          <div className="flex justify-between items-center">
            <div className="flex justify-start items-center gap-1.5">
              <Image src={currentLogo} alt="Logo" />
              {/* <span className="text-2xl font-semibold text-n700 dark:text-n30">
                QueryOne
              </span> */}
            </div>
            <div className="flex justify-start items-center gap-2">
              <button
                onClick={() => modalOpen("Search")}
                className="bg-white p-2 rounded-full flex justify-center items-center border border-primaryColor/20 dark:bg-n0"
              >
                <PiMagnifyingGlass />
              </button>
              <button
                onClick={() => setShowSidebar(false)}
                className="bg-white p-2 rounded-full flex justify-center items-center border border-primaryColor/20  dark:bg-n0"
              >
                <PiArrowUUpLeft />
              </button>
            </div>
          </div>
          <div className="flex flex-col gap-1 justify-start items-start pt-5 lg:pt-12 pb-5">
            <Link
              href={"/new-chat"}
              className="flex justify-center py-3 px-6 items-center gap-2 text-white bg-primaryColor rounded-xl"
            >
              <PiChatTeardropText size={20} />
              <span className="text-sm font-medium">New Chat</span>
            </Link>

          </div>
        </div>

        <div className="pb-5 flex-1 flex flex-col justify-start items-start w-full ">
          <p className="text-xs font-semibold text-n700 dark:text-n30">
            Recent
          </p>
          <div className="flex flex-col gap-1 w-full">
            <div className="flex flex-col gap-1 justify-start items-start w-full ">
              {chatList
                .slice(0, showAllRecentChats ? chatList.length : 6)
                .map(({ id, title, indexUsed }, idx) => (
                  <div key={id} className="flex justify-between items-center gap-2 hover:text-primaryColor hover:bg-primaryColor/10 rounded-xl duration-500 py-3 px-6 relative w-full group dropdown-container">
                    <div className="flex items-center gap-2">
                      <PiAlignLeft size={20} className="text-primaryColor" />
                      {renamingChatId === id ? (
                        <>
                          <input
                            autoFocus
                            value={newTitle}
                            onChange={(e) => setNewTitle(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === "Enter" && newTitle.trim()) {
                                renameChatById(id, newTitle.trim(), updateChatList);
                                setRenamingChatId(null);
                              } else if (e.key === "Escape") {
                                setRenamingChatId(null);
                              }
                            }}
                            className="text-sm bg-transparent border-b border-primaryColor outline-none w-[140px]"
                            placeholder="Enter title"
                          />
                          <button
                            onClick={() => {
                              if (newTitle.trim()) {
                                renameChatById(id, newTitle.trim(), updateChatList);
                                setRenamingChatId(null);
                              }
                            }}
                            className="ml-1 text-sm text-primaryColor"
                          >
                            ✔
                          </button>
                        </>
                      ) : (
                        <div className="relative">
                          <Link href={`/chat/${id}`} className="text-sm">
                            {title.slice(0, 20)}
                          </Link>
                          {/* Enhanced Tooltip for FAISS index information */}
                          <div
                            className="absolute bottom-full left-0 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap z-50 shadow-lg border border-gray-700"
                            role="tooltip"
                            aria-label={`FAISS Index: ${indexUsed || 'default'}`}
                          >
                            <div className="flex items-center gap-1">
                              <span className="text-gray-300">Category:</span>
                              <span className="font-medium text-white">
                                {indexUsed || 'default'}
                              </span>
                            </div>
                            {/* Tooltip arrow */}
                            <div className="absolute top-full left-3 w-0 h-0 border-l-[4px] border-r-[4px] border-t-[4px] border-transparent border-t-gray-900 dark:border-t-gray-800"></div>
                          </div>
                        </div>
                      )}
                    </div>
                    <button onClick={() => setShowMoreButton(idx === showMoreButton ? NaN : idx)}>
                      <PiDotsThreeBold className="text-xl" />
                    </button>
                    <ul className={`absolute top-9 right-0 bg-white dark:bg-n0 border border-primaryColor/30 p-3 rounded-xl flex flex-col gap-1 justify-start items-start text-sm z-40 text-n500 dark:text-n30 duration-300 ${showMoreButton === idx ? "visible translate-y-0 opacity-100" : "invisible translate-y-2 opacity-0"}`}>
                      <li
                        onClick={() => {
                          setRenamingChatId(id);
                          setNewTitle(title);
                          setShowMoreButton(NaN);
                        }}
                        className="flex justify-start items-center gap-1 py-2 px-3 hover:bg-primaryColor/5 cursor-pointer w-full"
                      >
                        <PiPencilLine />
                        <span>Rename</span>
                      </li>
                      {/* <li className="flex justify-start items-center gap-1 py-2 px-3 hover:bg-primaryColor/5 cursor-pointer w-full">
                        <PiShareFat />
                        <span>Share</span>
                      </li> */}
                      <li
                        // onClick={() => deleteChatById(id, updateChatList)}
                        onClick={() => {
                          setChatIdToDelete(id);
                          setShowDeleteModal(true);
                          setShowMoreButton(NaN); // Close the dropdown menu
                        }}

                        className="flex justify-start items-center gap-1 py-2 px-3 rounded-lg hover:bg-errorColor/5 text-errorColor cursor-pointer w-full"
                      >
                        <PiTrash />
                        <span>Delete</span>
                      </li>
                    </ul>
                  </div>
                ))}
            </div>
            <button
              onClick={() => setShowAllRecentChats((prev) => !prev)}
              className="flex justify-start items-center gap-2 py-3 px-6 hover:text-primaryColor hover:bg-primaryColor/10 rounded-xl duration-500 w-full"
            >
              {showAllRecentChats ? (
                <PiCaretUp className="text-xl text-primaryColor" />
              ) : (
                <PiCaretDown className="text-xl text-primaryColor" />
              )}
              <span className="text-sm font-medium">
                {showAllRecentChats ? "Less" : "More"}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>

    {/* 🧨 DELETE MODAL */}
    {showDeleteModal && (
  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
    <div className="bg-white dark:bg-n0 p-6 rounded-lg shadow-xl max-w-sm w-full">
      <h2 className="text-lg font-semibold text-n800 dark:text-n10 mb-4">
        Delete chat: <span className="text-blue-600">"{chatToDelete?.title}"</span>?
      </h2>
      <p className="text-sm text-n600 dark:text-n40 mb-6">
         Are you sure you want to delete this chat?
      </p>
      <div className="flex justify-end gap-3">
        <button
          onClick={() => {
            setShowDeleteModal(false);
            setChatIdToDelete(null);
            setShowMoreButton(NaN); // Ensure dropdown stays closed
          }}
          className="px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n800 rounded-md hover:bg-gray-300"
        >
          Cancel
        </button>
        <button
          onClick={() => {
            if (chatIdToDelete) {
              deleteChatById(chatIdToDelete, updateChatList);
              setShowDeleteModal(false);
              setChatIdToDelete(null);
              setShowMoreButton(NaN); // Ensure dropdown stays closed
            }
          }}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-errorColor rounded-md"
        >
          Delete
        </button>
      </div>
    </div>
  </div>
)}

  </>


  );
}

export default MainSidebar;