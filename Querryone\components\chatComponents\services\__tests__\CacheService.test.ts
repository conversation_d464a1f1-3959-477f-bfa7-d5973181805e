/**
 * Test suite for CacheService
 * Tests cache functionality including storage, retrieval, expiration, and statistics
 */

import { CacheService } from '../CacheService';

// Mock localStorage for testing
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

// Mock window object
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('CacheService', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorageMock.clear();
  });

  describe('Basic Cache Operations', () => {
    test('should cache and retrieve a response', () => {
      const query = 'What is the stock price of AAPL?';
      const mockResponse = {
        ai_response: 'Apple stock is currently trading at $150.',
        related_questions: ['What is Apple\'s market cap?'],
        sentence_analysis: []
      };

      // Cache the response
      CacheService.setCachedResponse(query, mockResponse);

      // Retrieve the cached response
      const cachedResponse = CacheService.getCachedResponse(query);

      expect(cachedResponse).not.toBeNull();
      expect(cachedResponse?.ai_response).toBe(mockResponse.ai_response);
      expect(cachedResponse?.related_questions).toEqual(mockResponse.related_questions);
      expect(cachedResponse?.query).toBe(query);
    });

    test('should return null for non-existent cache entries', () => {
      const result = CacheService.getCachedResponse('Non-existent query');
      expect(result).toBeNull();
    });

    test('should handle context-aware caching', () => {
      const query = 'What is the market trend?';
      const context1 = 'index1|<EMAIL>';
      const context2 = 'index2|<EMAIL>';
      
      const response1 = { ai_response: 'Response for context 1' };
      const response2 = { ai_response: 'Response for context 2' };

      // Cache responses with different contexts
      CacheService.setCachedResponse(query, response1, context1);
      CacheService.setCachedResponse(query, response2, context2);

      // Retrieve responses with specific contexts
      const cached1 = CacheService.getCachedResponse(query, context1);
      const cached2 = CacheService.getCachedResponse(query, context2);

      expect(cached1?.ai_response).toBe('Response for context 1');
      expect(cached2?.ai_response).toBe('Response for context 2');
    });
  });

  describe('Cache Statistics', () => {
    test('should track cache hits and misses', () => {
      const query = 'Test query for stats';
      const mockResponse = { ai_response: 'Test response' };

      // Initial stats should be zero
      let stats = CacheService.getCacheStats();
      expect(stats.totalQueries).toBe(0);
      expect(stats.cacheHits).toBe(0);
      expect(stats.cacheMisses).toBe(0);

      // Cache miss
      CacheService.getCachedResponse(query);
      stats = CacheService.getCacheStats();
      expect(stats.totalQueries).toBe(1);
      expect(stats.cacheMisses).toBe(1);
      expect(stats.cacheHits).toBe(0);

      // Cache the response
      CacheService.setCachedResponse(query, mockResponse);

      // Cache hit
      CacheService.getCachedResponse(query);
      stats = CacheService.getCacheStats();
      expect(stats.totalQueries).toBe(2);
      expect(stats.cacheMisses).toBe(1);
      expect(stats.cacheHits).toBe(1);
      expect(stats.hitRate).toBe(50);
    });

    test('should calculate hit rate correctly', () => {
      const mockResponse = { ai_response: 'Test response' };
      
      // Create multiple cache hits and misses
      CacheService.setCachedResponse('query1', mockResponse);
      CacheService.setCachedResponse('query2', mockResponse);
      
      // 2 hits
      CacheService.getCachedResponse('query1');
      CacheService.getCachedResponse('query2');
      
      // 1 miss
      CacheService.getCachedResponse('non-existent-query');

      const stats = CacheService.getCacheStats();
      expect(stats.totalQueries).toBe(3);
      expect(stats.cacheHits).toBe(2);
      expect(stats.cacheMisses).toBe(1);
      expect(stats.hitRate).toBe(66.67);
    });
  });

  describe('Cache Management', () => {
    test('should clear all cache data', () => {
      const mockResponse = { ai_response: 'Test response' };
      
      // Add some cache entries
      CacheService.setCachedResponse('query1', mockResponse);
      CacheService.setCachedResponse('query2', mockResponse);
      
      // Verify cache has data
      let stats = CacheService.getCacheStats();
      expect(stats.cacheSize).toBe(2);

      // Clear cache
      CacheService.clearCache();

      // Verify cache is empty
      stats = CacheService.getCacheStats();
      expect(stats.cacheSize).toBe(0);
      expect(stats.totalQueries).toBe(0);
      
      // Verify cached responses are gone
      expect(CacheService.getCachedResponse('query1')).toBeNull();
      expect(CacheService.getCachedResponse('query2')).toBeNull();
    });

    test('should handle LRU eviction when cache is full', () => {
      const mockResponse = { ai_response: 'Test response' };
      
      // Mock MAX_CACHE_SIZE to be small for testing
      const originalMaxSize = (CacheService as any).MAX_CACHE_SIZE;
      (CacheService as any).MAX_CACHE_SIZE = 3;

      // Add entries up to the limit
      CacheService.setCachedResponse('query1', mockResponse);
      CacheService.setCachedResponse('query2', mockResponse);
      CacheService.setCachedResponse('query3', mockResponse);

      // Verify all entries exist
      expect(CacheService.getCachedResponse('query1')).not.toBeNull();
      expect(CacheService.getCachedResponse('query2')).not.toBeNull();
      expect(CacheService.getCachedResponse('query3')).not.toBeNull();

      // Add one more entry to trigger eviction
      CacheService.setCachedResponse('query4', mockResponse);

      // The oldest entry should be evicted
      expect(CacheService.getCachedResponse('query1')).toBeNull();
      expect(CacheService.getCachedResponse('query4')).not.toBeNull();

      // Restore original MAX_CACHE_SIZE
      (CacheService as any).MAX_CACHE_SIZE = originalMaxSize;
    });
  });

  describe('Error Handling', () => {
    test('should handle localStorage errors gracefully', () => {
      // Mock localStorage to throw an error
      const originalSetItem = localStorageMock.setItem;
      localStorageMock.setItem = () => {
        throw new Error('localStorage is full');
      };

      // Should not throw an error
      expect(() => {
        CacheService.setCachedResponse('test', { ai_response: 'test' });
      }).not.toThrow();

      // Restore original setItem
      localStorageMock.setItem = originalSetItem;
    });

    test('should handle corrupted cache data', () => {
      // Set corrupted data in localStorage
      localStorageMock.setItem('financial_query_cache', 'invalid json');

      // Should return null and not throw an error
      const result = CacheService.getCachedResponse('test query');
      expect(result).toBeNull();
    });
  });
});
