import os
import json
import datetime
import faiss
import numpy as np
from dotenv import load_dotenv
from sentence_transformers import SentenceTransformer
import hashlib
from pathlib import Path
import asyncio
import concurrent.futures
from typing import Optional, Dict, Any, List, Tuple
import tempfile
import threading
import time
import io
import mimetypes

# Import language detection utilities
from language_utils import (
    detect_language_from_text,
    get_index_name_for_language,
    get_language_statistics
)

# Document processing libraries
try:
    import PyPDF2
    PDF_AVAILABLE = True
    print("✅ PyPDF2 available for PDF processing")
except ImportError:
    try:
        import pdfplumber
        PDF_AVAILABLE = True
        print("✅ pdfplumber available for PDF processing")
    except ImportError:
        PDF_AVAILABLE = False
        print("⚠️ No PDF processing library available")

try:
    from docx import Document as DocxDocument
    DOCX_AVAILABLE = True
    print("✅ python-docx available for Word document processing")
except ImportError:
    DOCX_AVAILABLE = False
    print("⚠️ python-docx not available for Word documents")

try:
    import textract
    TEXTRACT_AVAILABLE = True
    print("✅ textract available for additional document formats")
except ImportError:
    TEXTRACT_AVAILABLE = False
    print("⚠️ textract not available for additional formats")

# Load environment variables
load_dotenv()

# Configuration
FAISS_DATA_DIR = os.getenv("FAISS_DATA_DIR", "faiss_data")
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), FAISS_DATA_DIR)
DOCUMENTS_DIR = os.path.join(OUTPUT_DIR, "documents")
EMBED_MODEL = "all-MiniLM-L6-v2"
NLIST = 10  # Number of clusters for IVF

# Enhanced supported file extensions
SUPPORTED_EXTENSIONS = {
    '.pdf': 'PDF Document',
    '.docx': 'Word Document',
    '.doc': 'Word Document (Legacy)',
    '.txt': 'Text File',
    '.rtf': 'Rich Text Format',
    '.odt': 'OpenDocument Text',
    '.md': 'Markdown File',
    '.html': 'HTML Document',
    '.htm': 'HTML Document',
    '.csv': 'CSV File',
    '.json': 'JSON File',
    '.xml': 'XML Document'
}

# Additional configuration
MAX_CHUNK_SIZE = int(os.getenv("MAX_CHUNK_SIZE", "1000"))
CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "100"))
MAX_WORKERS = int(os.getenv("MAX_WORKERS", "4"))

# Ensure directories exist
os.makedirs(DOCUMENTS_DIR, exist_ok=True)

# Initialize embedding model
embedder = SentenceTransformer(EMBED_MODEL)

# Thread-safe progress tracking
progress_tracker = {}
progress_lock = threading.Lock()

def update_progress(process_id: str, progress: int, message: str = ""):
    """Update progress for a processing task"""
    with progress_lock:
        progress_tracker[process_id] = {
            'progress': progress,
            'message': message,
            'timestamp': time.time()
        }

def get_progress(process_id: str) -> Dict[str, Any]:
    """Get progress for a processing task"""
    with progress_lock:
        return progress_tracker.get(process_id, {'progress': 0, 'message': 'Not found'})

def get_document_id(file_path):
    """Generate a unique ID for the document based on file path and content"""
    file_name = os.path.basename(file_path)
    # Use file name and modification time for uniqueness
    try:
        mtime = os.path.getmtime(file_path)
        unique_string = f"{file_name}_{mtime}"
    except:
        unique_string = file_name
    return hashlib.md5(unique_string.encode()).hexdigest()[:12]

def extract_text_from_pdf(file_path):
    """Extract text from PDF file"""
    if not PDF_AVAILABLE:
        return None
    
    text = ""
    try:
        # Try pdfplumber first (better text extraction)
        if 'pdfplumber' in globals():
            import pdfplumber
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
        else:
            # Fallback to PyPDF2
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        
        return text.strip()
    except Exception as e:
        print(f"Error extracting text from PDF {file_path}: {e}")
        return None

def extract_text_from_docx(file_path):
    """Extract text from Word document"""
    if not DOCX_AVAILABLE:
        return None
    
    try:
        doc = DocxDocument(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text.strip()
    except Exception as e:
        print(f"Error extracting text from DOCX {file_path}: {e}")
        return None

def extract_text_from_txt(file_path):
    """Extract text from plain text file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read().strip()
    except UnicodeDecodeError:
        # Try with different encodings
        for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    return file.read().strip()
            except:
                continue
        print(f"Error: Could not decode text file {file_path}")
        return None
    except Exception as e:
        print(f"Error reading text file {file_path}: {e}")
        return None

def extract_text_with_textract(file_path):
    """Extract text using textract for various formats"""
    if not TEXTRACT_AVAILABLE:
        return None
    
    try:
        text = textract.process(file_path).decode('utf-8')
        return text.strip()
    except Exception as e:
        print(f"Error extracting text with textract from {file_path}: {e}")
        return None

def extract_text_from_document(file_path):
    """Extract text from document based on file extension"""
    file_ext = Path(file_path).suffix.lower()
    
    print(f"📄 Extracting text from {file_ext} file: {os.path.basename(file_path)}")
    
    # Choose extraction method based on file extension
    if file_ext == '.pdf':
        text = extract_text_from_pdf(file_path)
    elif file_ext in ['.docx']:
        text = extract_text_from_docx(file_path)
    elif file_ext == '.txt':
        text = extract_text_from_txt(file_path)
    else:
        # Try textract for other formats
        text = extract_text_with_textract(file_path)
        
        # If textract fails, try as plain text
        if not text:
            text = extract_text_from_txt(file_path)
    
    if text and len(text.strip()) > 0:
        print(f"✅ Extracted {len(text)} characters from document")
        return text
    else:
        print(f"❌ Could not extract text from document")
        return None

def chunk_text(text: str, size: int = 500):
    """Split text into chunks"""
    chunks, start = [], 0
    L = len(text)
    while start < L:
        end = min(start + size, L)
        if end < L:
            while end > start and not text[end].isspace():
                end -= 1
            if end == start:
                end = min(start + size, L)
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        start = end
    return chunks

def is_supported_file(file_path):
    """Check if file extension is supported"""
    file_ext = Path(file_path).suffix.lower()
    return file_ext in SUPPORTED_EXTENSIONS

def get_file_info(file_path):
    """Get basic file information"""
    try:
        file_stat = os.stat(file_path)
        file_ext = Path(file_path).suffix.lower()

        return {
            "name": os.path.basename(file_path),
            "size": file_stat.st_size,
            "extension": file_ext,
            "type": SUPPORTED_EXTENSIONS.get(file_ext, "Unknown"),
            "modified": datetime.datetime.fromtimestamp(file_stat.st_mtime).isoformat()
        }
    except Exception as e:
        print(f"Error getting file info for {file_path}: {e}")
        return None

def process_document_file(file_path, original_filename=None, index_name="default", user_selected_language=None):
    """Process a document file and add to selected FAISS index with multi-language support"""
    print(f"📄 Processing Document: {file_path}")
    print(f"🎯 Target index: {index_name}")
    if user_selected_language:
        print(f"🌐 User selected language: {user_selected_language}")

    # Check if file is supported
    if not is_supported_file(file_path):
        file_ext = Path(file_path).suffix.lower()
        print(f"❌ Unsupported file type: {file_ext}")
        print(f"Supported types: {list(SUPPORTED_EXTENSIONS.keys())}")
        return False

    # Get file information
    file_info = get_file_info(file_path)
    if not file_info:
        print("❌ Could not get file information")
        return False

    print(f"📊 File info: {file_info['name']} ({file_info['size']} bytes, {file_info['type']})")

    # Extract text from document
    document_text = extract_text_from_document(file_path)
    if not document_text:
        print("❌ Could not extract text from document")
        return False

    print(f"✅ Extracted text: {len(document_text)} characters")

    # Detect language from document content
    detected_language = detect_language_from_text(document_text)
    print(f"🔍 Detected language: {detected_language}")

    # Get language statistics for detailed analysis
    lang_stats = get_language_statistics(document_text)
    if lang_stats:
        print("📊 Language distribution:")
        for lang, ratio in sorted(lang_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {lang}: {ratio:.3f} ({ratio*100:.1f}%)")

    # Determine final index name based on language detection and user preference
    if index_name == "default" or index_name == "auto":
        # Auto-detect index based on content language
        final_index_name = get_index_name_for_language(detected_language, user_selected_language)
    else:
        # Use user-specified index
        final_index_name = index_name
        print(f"🎯 Using user-specified index: {final_index_name}")

    print(f"📁 Final target index: {final_index_name}")

    # Generate document ID
    doc_id = get_document_id(file_path)

    # Load or create the target FAISS index
    try:
        # Import FAISS index management functions
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from full_code import load_faiss_index, create_faiss_index

        # Try to load existing index using final index name
        faiss_index, existing_metadata, success = load_faiss_index(final_index_name)

        if not success or faiss_index is None:
            print(f"📁 Creating new FAISS index: {final_index_name}")
            # Create new index if it doesn't exist
            dummy_embedding = embedder.encode(["hello world"])
            dim = len(dummy_embedding[0])
            result = create_faiss_index(final_index_name, dimension=dim, embed_model=EMBED_MODEL)
            if not result.get("success"):
                print(f"❌ Failed to create index: {result.get('error')}")
                return False

            # Load the newly created index
            faiss_index, existing_metadata, success = load_faiss_index(final_index_name)
            if not success:
                print(f"❌ Failed to load newly created index")
                return False

        print(f"✅ Loaded FAISS index: {final_index_name} with {len(existing_metadata)} existing entries")

    except Exception as e:
        print(f"❌ Error loading FAISS index: {e}")
        return False

    # Process document chunks
    chunks = chunk_text(document_text)
    record_date = datetime.datetime.now().isoformat()
    new_vectors = []
    new_metadata = []

    for chunk_idx, chunk in enumerate(chunks):
        # Create embedding for this chunk
        vec_list = embedder.encode([chunk])
        vec = np.array(vec_list[0], dtype="float32")
        new_vectors.append(vec)

        new_metadata.append({
            "chunk_text": chunk,
            "record_date": record_date,
            "category": "document",
            "url": f"file://{file_path}",
            "document_id": doc_id,
            "title": original_filename or file_info["name"],
            "file_name": file_info["name"],
            "file_type": file_info["type"],
            "file_extension": file_info["extension"],
            "file_size": file_info["size"],
            "vector_id": f"document-{doc_id}-chunk-{chunk_idx}",
            "source_type": "document",
            "upload_source": "document_upload",
            "detected_language": detected_language,
            "index_name": final_index_name,
            "language_stats": lang_stats,
            "user_selected_language": user_selected_language
        })

        print(f"📝 Embedded chunk {chunk_idx + 1}/{len(chunks)}: {chunk[:60]}...")

    if not new_vectors:
        print("❌ No content to index")
        return False

    # Stack & normalize for cosine similarity
    xb = np.vstack(new_vectors)
    faiss.normalize_L2(xb)

    # Add vectors to the existing index
    faiss_index.add(xb)

    # Combine existing and new metadata
    combined_metadata = existing_metadata + new_metadata

    # Save updated index and metadata
    index_dir = os.path.join(OUTPUT_DIR, final_index_name)
    os.makedirs(index_dir, exist_ok=True)

    faiss_file_path = os.path.join(index_dir, f"{final_index_name}.faiss")
    metadata_file_path = os.path.join(index_dir, f"{final_index_name}.json")

    # Save FAISS index
    faiss.write_index(faiss_index, faiss_file_path)
    print(f"🧠 Updated FAISS index saved to {faiss_file_path}")

    # Save metadata
    with open(metadata_file_path, "w", encoding="utf-8") as f:
        json.dump(combined_metadata, f, ensure_ascii=False, indent=2)
    print(f"🗃️ Updated metadata saved to {metadata_file_path}")

    print(f"✅ Finished: Added {len(new_vectors)} chunks to index '{final_index_name}' (total: {faiss_index.ntotal})")
    print(f"🌐 Content language: {detected_language}")
    print(f"📁 Stored in index: {final_index_name}")
    return True

def main():
    print("📄 Document Processor")
    print("=" * 50)
    print(f"📄 PDF processing: {'✅' if PDF_AVAILABLE else '❌'}")
    print(f"📝 DOCX processing: {'✅' if DOCX_AVAILABLE else '❌'}")
    print(f"🔧 Textract processing: {'✅' if TEXTRACT_AVAILABLE else '❌'}")
    print(f"📁 Documents directory: {DOCUMENTS_DIR}")
    print(f"🎯 Supported formats: {list(SUPPORTED_EXTENSIONS.keys())}")

    # Test with a document file
    test_file = input("\nEnter document file path: ").strip()
    if test_file and os.path.exists(test_file):
        success = process_document_file(test_file)
        if success:
            print("🎉 Document processed successfully!")
        else:
            print("❌ Failed to process document")
    else:
        print("❌ File not found or invalid path")

if __name__ == "__main__":
    main()
