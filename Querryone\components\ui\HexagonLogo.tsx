import React from 'react';

interface HexagonLogoProps {
  size?: number;
  color?: string;
  className?: string;
}

const HexagonLogo: React.FC<HexagonLogoProps> = ({ 
  size = 24, 
  color = '#4d6bfe',
  className = '' 
}) => {
  return (
    <div 
      className={`hexagon-logo ${className}`}
      style={{ width: size, height: size }}
    >
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 24 24" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Hexagon shape */}
        <path 
          d="M12 2L21.6 7V17L12 22L2.4 17V7L12 2Z" 
          fill="#1a2231" 
          stroke={color} 
          strokeWidth="1.5" 
        />
        
        {/* Inner cube/3D effect */}
        <path 
          d="M12 7L16 9.5V14.5L12 17L8 14.5V9.5L12 7Z" 
          fill={color} 
          fillOpacity="0.3" 
          stroke={color} 
          strokeWidth="0.5" 
        />
        
        {/* Center dot */}
        <circle 
          cx="12" 
          cy="12" 
          r="1.5" 
          fill={color} 
        />
      </svg>
    </div>
  );
};

export default HexagonLogo;
