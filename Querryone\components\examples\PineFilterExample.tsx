/**
 * Example Component: PINE Collection Email-Based Filtering
 * 
 * This component demonstrates how to implement email-based filtering for PINE collection
 * to ensure users only see their own data based on email authentication.
 */

import React, { useState, useEffect } from 'react';
import { usePineFilter, usePineIndexAccess, useFilteredIndices } from '../../hooks/usePineFilter';
import { PineFilterService } from '../../services/pineFilterService';

interface PineFilterExampleProps {
  userEmail?: string;
  className?: string;
}

const PineFilterExample: React.FC<PineFilterExampleProps> = ({ 
  userEmail, 
  className = '' 
}) => {
  // State for demonstration
  const [selectedIndex, setSelectedIndex] = useState<string>('');
  const [testIndices] = useState<string[]>(['default', 'financialnews', 'documents', 'articles']);
  const [validationResult, setValidationResult] = useState<string>('');

  // Use the PINE filter hook
  const {
    userIndices,
    isLoading,
    error,
    hasAccess,
    userEmail: currentUserEmail,
    refreshIndices,
    validateAccess,
    filterIndices,
    getSafeIndex,
    checkHasIndices
  } = usePineFilter(userEmail);

  // Use index access validation hook
  const {
    hasAccess: indexHasAccess,
    isValidating,
    error: accessError,
    revalidate
  } = usePineIndexAccess(selectedIndex, userEmail);

  // Use filtered indices hook
  const {
    filteredIndices,
    isFiltering,
    error: filterError
  } = useFilteredIndices(testIndices, userEmail);

  // Handle index selection
  const handleIndexSelect = (index: string) => {
    setSelectedIndex(index);
    setValidationResult('');
  };

  // Handle manual validation
  const handleValidateAccess = async () => {
    if (!selectedIndex) {
      setValidationResult('Please select an index first');
      return;
    }

    const hasAccess = await validateAccess(selectedIndex);
    setValidationResult(
      hasAccess 
        ? `✅ Access granted to index: ${selectedIndex}` 
        : `❌ Access denied to index: ${selectedIndex}`
    );
  };

  // Handle getting safe index
  const handleGetSafeIndex = async () => {
    const safeIndex = await getSafeIndex();
    setSelectedIndex(safeIndex);
    setValidationResult(`Safe index selected: ${safeIndex}`);
  };

  // Handle checking if user has indices
  const handleCheckHasIndices = async () => {
    const hasIndices = await checkHasIndices();
    setValidationResult(
      hasIndices 
        ? '✅ User has accessible indices' 
        : '❌ User has no accessible indices'
    );
  };

  return (
    <div className={`pine-filter-example p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg ${className}`}>
      <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
        PINE Collection Email-Based Filtering Demo
      </h2>

      {/* User Info Section */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
          Current User
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Email: <span className="font-mono">{currentUserEmail || 'Not logged in'}</span>
        </p>
        <p className="text-gray-600 dark:text-gray-400">
          Has Access: <span className={hasAccess ? 'text-green-600' : 'text-red-600'}>
            {hasAccess ? 'Yes' : 'No'}
          </span>
        </p>
      </div>

      {/* User Indices Section */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">
            User's Accessible Indices
          </h3>
          <button
            onClick={refreshIndices}
            disabled={isLoading}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Refresh'}
          </button>
        </div>

        {error && (
          <div className="mb-3 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded">
            Error: {error}
          </div>
        )}

        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {userIndices.map((index) => (
            <button
              key={index}
              onClick={() => handleIndexSelect(index)}
              className={`p-2 rounded border text-sm ${
                selectedIndex === index
                  ? 'bg-blue-500 text-white border-blue-500'
                  : 'bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-500 hover:bg-gray-200 dark:hover:bg-gray-500'
              }`}
            >
              {index}
            </button>
          ))}
        </div>

        {userIndices.length === 0 && !isLoading && (
          <p className="text-gray-500 dark:text-gray-400 italic">
            No accessible indices found for this user.
          </p>
        )}
      </div>

      {/* Filtered Indices Section */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">
          Filtered Test Indices
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
          Original indices: {testIndices.join(', ')}
        </p>
        
        {filterError && (
          <div className="mb-3 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded">
            Filter Error: {filterError}
          </div>
        )}

        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded">
          {isFiltering ? (
            <p className="text-gray-500">Filtering indices...</p>
          ) : (
            <p className="text-gray-700 dark:text-gray-300">
              Filtered indices: <span className="font-mono">{filteredIndices.join(', ') || 'None'}</span>
            </p>
          )}
        </div>
      </div>

      {/* Index Access Validation Section */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">
          Index Access Validation
        </h3>
        
        <div className="flex flex-wrap gap-2 mb-3">
          <button
            onClick={handleValidateAccess}
            disabled={!selectedIndex || isValidating}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {isValidating ? 'Validating...' : 'Validate Access'}
          </button>
          
          <button
            onClick={handleGetSafeIndex}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Get Safe Index
          </button>
          
          <button
            onClick={handleCheckHasIndices}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Check Has Indices
          </button>
        </div>

        {selectedIndex && (
          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded mb-3">
            <p className="text-gray-700 dark:text-gray-300">
              Selected Index: <span className="font-mono">{selectedIndex}</span>
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              Real-time Access: <span className={indexHasAccess ? 'text-green-600' : 'text-red-600'}>
                {isValidating ? 'Checking...' : (indexHasAccess ? 'Granted' : 'Denied')}
              </span>
            </p>
          </div>
        )}

        {accessError && (
          <div className="mb-3 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded">
            Access Error: {accessError}
          </div>
        )}

        {validationResult && (
          <div className="p-3 bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded">
            {validationResult}
          </div>
        )}
      </div>

      {/* Usage Instructions */}
      <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
        <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
          How to Use Email-Based Filtering:
        </h4>
        <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
          <li>• Users only see indices/records that belong to their email</li>
          <li>• Email matching is case-insensitive and handles whitespace</li>
          <li>• Access validation prevents unauthorized data access</li>
          <li>• Filtering is applied automatically in all PINE collection queries</li>
          <li>• Use the hooks and services for easy integration in your components</li>
        </ul>
      </div>
    </div>
  );
};

export default PineFilterExample;
