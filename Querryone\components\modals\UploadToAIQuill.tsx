import SmallButtons from "@/components/ui/buttons/SmallButtons";
import InputFieldSecond from "@/components/ui/InputFieldSecond";
import React, { useState } from "react";
import { PiCloudArrowUp } from "react-icons/pi";
import FileUpload from "@/components/FileUpload";
import { mockUploadFile } from "@/services/mockServer";

function UploadToAIQuill() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const handleUpload = () => {
    if (!selectedFile) {
      setUploadError("Please select a file first");
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    // Use the mock upload service for now
    mockUploadFile(selectedFile)
      .then(response => {
        console.log('File uploaded successfully:', response);
        setUploadSuccess(true);
        setIsUploading(false);
      })
      .catch(error => {
        console.error('Error uploading file:', error);
        setUploadError(error.message);
        setIsUploading(false);
      });
  };

  return (
    <div className="pt-6">
      <p className="text-sm text-n500">Upload a file</p>

      <FileUpload
        onFileUpload={(files) => {
          if (files && files.length > 0) {
            setSelectedFile(files[0]);
            setUploadSuccess(false);
            setUploadError(null);
          }
        }}
        maxFileSize={50}
        allowedTypes={['image/jpeg', 'image/png', 'application/pdf', 'video/mp4', 'text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']}
      />

      <div className="pt-4">
        <InputFieldSecond
          className=""
          placeholder="https://demo.com"
          title="Or reference url"
        />
      </div>

      {uploadError && (
        <div className="mt-3 text-red-500 text-sm">
          {uploadError}
        </div>
      )}

      {uploadSuccess && (
        <div className="mt-3 text-green-500 text-sm">
          File uploaded successfully!
        </div>
      )}

      <div className="flex justify-start items-center gap-2 pt-5 text-xs">
        <SmallButtons
          name={isUploading ? "Uploading..." : "Upload Now"}
          onClick={handleUpload}
          disabled={isUploading || !selectedFile}
        />
        <SmallButtons name="Cancel" secondary={true} />
      </div>
    </div>
  );
}

export default UploadToAIQuill;
