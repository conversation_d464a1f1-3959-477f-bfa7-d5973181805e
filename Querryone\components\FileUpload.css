.file-upload-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.drop-area {
  border: 2px dashed #4a90e2;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(74, 144, 226, 0.05);
  margin-bottom: 20px;
}

.drop-area.dragging {
  background-color: rgba(74, 144, 226, 0.1);
  border-color: #2a70c2;
  transform: scale(1.01);
}

.drop-icon {
  color: #4a90e2;
  margin-bottom: 15px;
}

.drop-text {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.file-restriction-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 4px;
  padding: 8px 12px;
  margin: 0 auto 12px;
  max-width: 80%;
}

.restriction-icon {
  color: #4a90e2;
  font-size: 16px;
  margin-right: 8px;
  font-weight: bold;
}

.file-restriction-notice span:last-child {
  font-size: 14px;
  color: #4a90e2;
  font-weight: 500;
}

.file-types {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.file-input {
  display: none;
}

.file-list {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.file-list h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #333;
}

.file-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.file-item {
  display: flex;
  flex-direction: column;
  padding: 12px;
  border-radius: 6px;
  background-color: white;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.file-name {
  font-weight: 500;
  color: #333;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  margin-left: 10px;
}

.file-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.upload-btn, .remove-btn {
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.upload-btn {
  background-color: #4a90e2;
  color: white;
  margin-right: 10px;
}

.upload-btn:hover {
  background-color: #2a70c2;
}

.remove-btn {
  background-color: transparent;
  color: #ff4d4f;
  font-size: 16px;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  color: #cf1322;
}

.progress-container {
  flex-grow: 1;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 10px;
  position: relative;
}

.progress-bar {
  height: 100%;
  background-color: #4a90e2;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  right: -30px;
  top: -5px;
  font-size: 12px;
  color: #666;
}

.success-icon {
  color: #52c41a;
  font-size: 18px;
  margin-right: 10px;
}

.error-icon {
  color: #ff4d4f;
  font-size: 18px;
  margin-right: 10px;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 5px;
}

.upload-all-btn {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 10px;
  width: 100%;
  transition: background-color 0.2s;
}

.upload-all-btn:hover {
  background-color: #2a70c2;
}

.upload-single-btn {
  display: block;
  width: 100%;
  padding: 10px 16px;
  margin-top: 15px;
  font-size: 15px;
  font-weight: 500;
  background-color: #4a90e2;
  background-image: linear-gradient(to right, #4a90e2, #357abf);
  box-shadow: 0 2px 5px rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
}

.upload-single-btn:hover {
  background-image: linear-gradient(to right, #357abf, #2a70c2);
  box-shadow: 0 3px 8px rgba(74, 144, 226, 0.4);
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .drop-area {
    padding: 20px;
  }

  .drop-text {
    font-size: 16px;
  }

  .file-restriction-notice {
    max-width: 95%;
    padding: 6px 10px;
  }

  .restriction-icon {
    font-size: 14px;
  }

  .file-restriction-notice span:last-child {
    font-size: 13px;
  }

  .file-types {
    font-size: 12px;
  }

  .upload-single-btn {
    padding: 8px 14px;
    font-size: 14px;
  }
}
