"use client";
import React, { useState, useEffect } from "react";
import { PiArrowClockwise } from "react-icons/pi";
import NewsCard from "@/components/NewsCard";
import NewsCardSkeleton from "@/components/NewsCardSkeleton";
import CategoryTab from "@/components/CategoryTab";
import { fetchNews } from "@/services/api";

function Explore() {
  const [newsItems, setNewsItems] = useState([]);
  const [categories, setCategories] = useState(['all', 'business', 'markets', 'economy', 'technology', 'banking', 'policy']);
  const [currentCategory, setCurrentCategory] = useState('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadNews = async (category = currentCategory) => {
    try {
      setLoading(true);
      setError(null);
      console.log(`Loading news for category: ${category}`);
      const data = await fetchNews(category);

      // Handle the new response format
      if (data.news) {
        console.log(`Received ${data.news.length} news items for category: ${category}`);
        console.log(`Categories in response: ${data.categories?.join(', ')}`);

        // Log the categories of the received news items
        const categoryDistribution = data.news.reduce((acc, item) => {
          acc[item.category] = (acc[item.category] || 0) + 1;
          return acc;
        }, {});
        console.log('Category distribution:', categoryDistribution);

        setNewsItems(data.news);

        // Update categories if provided by the API
        if (data.categories && Array.isArray(data.categories)) {
          setCategories(data.categories);
        }

        // Update current category if provided
        if (data.current_category) {
          setCurrentCategory(data.current_category);
        }
      } else {
        // Fallback for backward compatibility
        console.log('Using fallback response format');
        setNewsItems(data);
      }
    } catch (err) {
      console.error('Error loading news:', err);
      setError('Failed to load news. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle category change
  const handleCategoryChange = (category: string) => {
    if (category !== currentCategory) {
      setCurrentCategory(category);
      loadNews(category);
    }
  };

  useEffect(() => {
    loadNews();
  }, []);
  return (
    <div className="w-full overflow-auto flex justify-center items-start py-8">
      <div className="flex flex-col justify-center items-center px-6 max-w-[1080px] w-full">
        <div className="text-center mb-8">
          <p className="text-3xl font-semibold text-n700 dark:text-n30">
            Financial Dashboard
          </p>
          <p className="pt-3 max-w-[600px] text-n100 dark:text-n50">
            Stay informed with the latest Indian financial news
          </p>
        </div>
        {/* News Section */}
        <div className="w-full">
          <div className="flex flex-col justify-center items-center text-center gap-3 mb-6">
            <p className="text-2xl text-n700 font-semibold dark:text-n30">
              Top Financial News
            </p>
            <p className="text-n100 dark:text-n50 max-w-[600px]">
              Stay updated with the latest financial news and market trends from trusted sources
            </p>
          </div>

          {/* Category Navigation Bar */}
          <div className="w-full mb-8">
            <div className="flex flex-wrap justify-center items-center gap-2 pb-2 overflow-x-auto">
              {categories.map((category) => (
                <CategoryTab
                  key={category}
                  category={category}
                  isActive={currentCategory === category}
                  onClick={() => handleCategoryChange(category)}
                />
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {loading ? (
              // Show skeletons while loading
              Array(6).fill().map((_, index) => (
                <NewsCardSkeleton key={index} />
              ))
            ) : error ? (
              // Show error message if there's an error
              <div className="col-span-full text-center py-10 bg-white dark:bg-n0 rounded-lg border border-primaryColor/20 p-6">
                <h3 className="text-xl mb-3 text-n700 dark:text-n30">Error loading news</h3>
                <p className="text-n100 dark:text-n50">{error}</p>
              </div>
            ) : newsItems.length === 0 ? (
              // Show message if no news items
              <div className="col-span-full text-center py-10 bg-white dark:bg-n0 rounded-lg border border-primaryColor/20 p-6">
                <h3 className="text-xl mb-3 text-n700 dark:text-n30">No news found</h3>
                <p className="text-n100 dark:text-n50">Check back later for updates.</p>
              </div>
            ) : (
              // Show news items
              newsItems.map((item, index) => (
                <NewsCard key={index} news={item} />
              ))
            )}
          </div>

          <div className="w-full pt-10 text-center">
            <div className="flex flex-col items-center">
              {currentCategory !== 'all' && (
                <p className="text-sm text-n100 dark:text-n50 mb-2">
                  Showing news for category: <span className="font-medium text-primaryColor">{currentCategory.charAt(0).toUpperCase() + currentCategory.slice(1)}</span>
                </p>
              )}
              <button
                className="border border-primaryColor rounded-full py-3 px-8 text-center text-primaryColor text-sm font-medium inline-flex items-center justify-center gap-2 hover:bg-primaryColor hover:text-white transition-colors duration-300"
                onClick={() => loadNews(currentCategory)}
                disabled={loading}
              >
                <PiArrowClockwise className={loading ? "animate-spin" : ""} />
                {loading ? 'Loading...' : 'Refresh News'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Explore;
