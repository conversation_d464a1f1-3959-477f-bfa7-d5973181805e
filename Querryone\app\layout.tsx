import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";

const InterFont = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Queryone",
  description: "Advanced AI Chat Interface with Bot Creation",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Prevent browser extensions from interfering during hydration
              if (typeof window !== 'undefined') {
                window.__REACT_HYDRATION_STARTED__ = true;
              }
            `,
          }}
        />
      </head>
      <body className={`${InterFont.variable} `} suppressHydrationWarning>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
