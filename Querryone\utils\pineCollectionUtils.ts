/**
 * Utility functions for handling PINE collection operations
 * Provides centralized logic for creating and fetching user configurations
 * Updated to support FAISS integration and atomic delete operations
 */

// Default configuration for new users
export const DEFAULT_PINE_CONFIG = {
  api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
  index_name: "financialnews"
};

// Base URL for PINE collection API
const PINE_API_BASE = "https://dev-commonmannit.mannit.co/mannit";

/**
 * Creates a new PINE collection entry for a user with default values
 * @param userEmail - The user's email address
 * @returns Promise<object | null> - The created configuration or null if failed
 */
export const createPineCollectionEntry = async (userEmail: string) => {
  try {
    console.log(`Creating new PINE collection entry for user: ${userEmail}`);

    // Default values for new users
    const defaultConfig = {
      client: userEmail,
      api_key: DEFAULT_PINE_CONFIG.api_key,
      index_name: DEFAULT_PINE_CONFIG.index_name
    };

    const response = await fetch(`${PINE_API_BASE}/eCreateCol?colname=PINE`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "xxxid": "PINE"
      },
      body: JSON.stringify(defaultConfig)
    });

    if (!response.ok) {
      console.error("Failed to create PINE collection entry");
      return null;
    }

    console.log("Successfully created PINE collection entry for new user:", userEmail);
    return defaultConfig;
  } catch (error) {
    console.error("Error creating PINE collection entry:", error);
    return null;
  }
};

/**
 * Fetches PINE collection data for a user, returns default config if not exists
 * @param userEmail - The user's email address
 * @returns Promise<object> - The user's configuration (existing or default)
 */
export const fetchPineConfig = async (userEmail: string) => {
  try {
    if (!userEmail) {
      console.warn("No user email provided - using default configuration");
      return DEFAULT_PINE_CONFIG;
    }

    // Use filtered API endpoint to get ALL entries for this user (remove filtercount=1 to get all matches)
    const filterUrl = `${PINE_API_BASE}/retrievecollection?ColName=PINE&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(userEmail.trim())}`;

    const response = await fetch(filterUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "xxxid": "PINE"
      }
    });

    if (!response.ok) {
      console.error("Failed to fetch PINE collection data - using default configuration");
      return DEFAULT_PINE_CONFIG;
    }

    const data = await response.json();
    console.log("PINE collection response:", data);

    // Check if user exists in PINE collection and extract all their indexes
    if (data.statusCode === 200 && data.source && data.source.length > 0) {
      // Parse each item in the source array (they are JSON strings)
      const pineData = data.source.map((item: string) => JSON.parse(item));

      // Extract all indexes and API keys for this user
      const userIndexes: string[] = [];
      const userApiKeys: string[] = [];
      let firstUserEntry = null;

      pineData.forEach((item: any) => {
        if (item.client && item.client.trim().toLowerCase() === userEmail.trim().toLowerCase()) {
          if (!firstUserEntry) firstUserEntry = item; // Keep first entry for return
          if (item.index_name && !userIndexes.includes(item.index_name)) {
            userIndexes.push(item.index_name);
          }
          if (item.api_key && !userApiKeys.includes(item.api_key)) {
            userApiKeys.push(item.api_key);
          }
        }
      });

      if (userIndexes.length > 0 && userApiKeys.length > 0) {
        // User exists in PINE collection - use their configuration
        console.log("Found existing PINE data for user:", userEmail);
        console.log("User indexes:", userIndexes);
        console.log("User API keys count:", userApiKeys.length);

        // Store all indexes and API keys for later use
        if (typeof window !== 'undefined') {
          localStorage.setItem('pineconeApiKeys', JSON.stringify(userApiKeys));
          localStorage.setItem('userPineconeIndexes', JSON.stringify(userIndexes));
        }

        return {
          api_key: userApiKeys[0], // Return first API key as default
          index_name: userIndexes[0], // Return first index as default
          all_indexes: userIndexes, // Include all indexes
          all_api_keys: userApiKeys // Include all API keys
        };
      } else {
      // User doesn't exist in PINE collection - use default values without auto-creation
      console.log("No PINE data found for user:", userEmail, "- using default configuration without auto-creation");
      return DEFAULT_PINE_CONFIG;
    }
  } catch (error) {
    console.error("Error in fetchOrCreatePineConfig:", error);
    // Fallback to default values on error
    console.log("Using fallback default configuration due to error");
    return DEFAULT_PINE_CONFIG;
  }
};

/**
 * Creates a PINE collection entry for a FAISS index
 * @param userEmail - The user's email address
 * @param indexName - The FAISS index name
 * @param embedModel - The embedding model used
 * @returns Promise<boolean> - Success status
 */
export const createPineEntryForFaissIndex = async (
  userEmail: string,
  indexName: string,
  embedModel: string = 'all-MiniLM-L6-v2'
): Promise<boolean> => {
  try {
    console.log(`Creating PINE entry for FAISS index: ${indexName} (user: ${userEmail})`);

    const config = {
      client: userEmail,
      index_name: indexName,
      api_key: 'faiss-local', // Identifier for FAISS-based indexes
      embed_model: embedModel
    };

    const response = await fetch(`${PINE_API_BASE}/eCreateCol?colname=PINE`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "xxxid": "PINE"
      },
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      console.error("Failed to create PINE entry for FAISS index");
      return false;
    }

    console.log("Successfully created PINE entry for FAISS index:", indexName);
    return true;
  } catch (error) {
    console.error("Error creating PINE entry for FAISS index:", error);
    return false;
  }
};

/**
 * Stores PINE configuration in localStorage
 * @param config - The configuration object with api_key and index_name
 */
export const storePineConfigInLocalStorage = (config: { api_key: string; index_name: string }) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem("pinecone_api_key", config.api_key);
    localStorage.setItem("pinecone_index_name", config.index_name);
    console.log("Stored PINE configuration in localStorage");
  }
};

/**
 * Gets PINE configuration from localStorage
 * @returns object | null - The stored configuration or null if not found
 */
export const getPineConfigFromLocalStorage = () => {
  if (typeof window !== 'undefined') {
    const apiKey = localStorage.getItem("pinecone_api_key");
    const indexName = localStorage.getItem("pinecone_index_name");

    if (apiKey && indexName) {
      return { api_key: apiKey, index_name: indexName };
    }
  }
  return null;
};

/**
 * Complete function to handle user PINE configuration
 * Fetches existing config or uses default, then stores in localStorage
 * @param userEmail - The user's email address
 * @returns Promise<object> - The final configuration
 */
export const handleUserPineConfiguration = async (userEmail: string) => {
  try {
    // First check if we already have config in localStorage
    const existingConfig = getPineConfigFromLocalStorage();
    if (existingConfig) {
      console.log("Using existing PINE configuration from localStorage");
      return existingConfig;
    }

    // Fetch configuration (without auto-creation)
    const config = await fetchPineConfig(userEmail);

    // Store in localStorage
    storePineConfigInLocalStorage(config);

    return config;
  } catch (error) {
    console.error("Error in handleUserPineConfiguration:", error);
    // Fallback to default
    const defaultConfig = DEFAULT_PINE_CONFIG;
    storePineConfigInLocalStorage(defaultConfig);
    return defaultConfig;
  }
};
