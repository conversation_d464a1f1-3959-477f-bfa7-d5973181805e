"use client";
import React, { useEffect, useRef, useState } from "react";
import UserMessage from "@/components/chatComponents/UserMessage";
import ChatBox from "@/components/chatComponents/ChatBox";
import { usePathname } from "next/navigation";
import BotReply from "@/components/chatComponents/BotReply";
import { Chat, useChatHandler } from "@/stores/chatList";

function CustomChat() {
  const [scroll, setScroll] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { chatList, userQuery, updateChatList } = useChatHandler();
  const path = usePathname();
  const [currentChat, setCurrentChat] = useState<Chat>();
  const [selectedLanguage, setSelectedLanguage] = useState("English");

  const chatId = path ? path.split("/chat/")[1] : null;

  // Load chat list when component mounts
  useEffect(() => {
    updateChatList();
  }, [updateChatList]);

  useEffect(() => {
    const currentChatList = chatList.find(({ id }: { id: string }) => {
      return id === chatId;
    });
    setCurrentChat(currentChatList);
  }, [chatList, path, chatId]);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [userQuery, scroll]);

  return (
    <div className=" flex flex-col gap-4 h-full flex-1 overflow-auto w-full z-20 ">
      <div className="overflow-auto w-full flex-1" ref={chatContainerRef}>
        <div className={`pb-6  flex-grow  w-full max-w-[1070px] mx-auto `}>
          <div className="flex gap-3 px-6 relative z-20  w-full flex-col ">
            {currentChat &&
              currentChat.messages.map((item, idx) => {
                return (
                  <div className="flex flex-col gap-3" key={idx}>
                    {item.isUser && typeof item.text === "string" && (
                      <UserMessage
                        message={item.text}
                        timestamp={item.timestamp}
                        uploadedFiles={item.uploadedFiles}
                        uploadedURLs={item.uploadedURLs}
                        selectedLanguage={selectedLanguage}
                      />
                    )}

                    {!item.isUser && (
                      <BotReply
                        replyType="response"
                        setScroll={setScroll}
                        isAnimation={userQuery === item.text}
                        aiResponse={item.text}
                        timestamp={item.timestamp}
                      />
                    )}
                  </div>
                );
              })}
          </div>
        </div>
      </div>

      <ChatBox />
    </div>
  );
}

export default CustomChat;
