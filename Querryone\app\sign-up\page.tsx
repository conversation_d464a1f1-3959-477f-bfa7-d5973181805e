
"use client";
import React, { useState, useEffect } from "react";
import displayImg from "@/public/images/sign-up-page-img.png";
import Image from "next/image";
import logoLight from "@/public/images/logo5.png";
import logoDark from "@/public/images/logo6.png";
// import FormInput from "@/components/ui/FormInput";
import GradientBackground from "@/components/ui/GradientBackground";
import Footer from "@/components/Footer";
import { PiFacebookLogo, PiGoogleLogo, PiInstagramLogo } from "react-icons/pi";
import Link from "next/link";
import { useTheme } from "next-themes";
import { baseUrl, uid } from "@/components/api/api";



function SignUp() {
  const [formData, setFormData] = useState({
    username: "",
    mobile: "",
    email: "",
    password: ""
  });

  const [errors, setErrors] = useState({
    username: "",
    mobile: "",
    email: "",
    password: ""
  });

  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState(""); // 'success' or 'error'
  const [currentLogo, setCurrentLogo] = useState(logoLight);

  const { resolvedTheme } = useTheme();

  // Update logo based on theme
  useEffect(() => {
    setCurrentLogo(resolvedTheme === 'dark' ? logoDark : logoLight);
  }, [resolvedTheme]);
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === "mobile") {
      let numericValue = value.replace(/\D/g, '');

      if (numericValue.length > 10) {
        numericValue = numericValue.slice(0, 10);
      }

      setFormData({ ...formData, [name]: numericValue });
      setErrors(prev => ({ ...prev, [name]: "" }));
    } else {
      setFormData({ ...formData, [name]: value });
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };


  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    setMessage("");
    setErrors({ username: "", mobile: "", email: "", password: "" });
    const newErrors: any = {};
    if (!formData.username.trim()) newErrors.username = "Name required";
    if (!formData.mobile.trim()) newErrors.mobile = "Mobile number required";
    else if (formData.mobile.length !== 10) newErrors.mobile = "Mobile number must be 10 digits";
    if (!formData.email.trim()) newErrors.email = "Email required";
    if (!formData.password.trim()) newErrors.password = "Password required";

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    setLoading(true);
    // Replace `uid` with the actual user ID or session ID
    const uid = "QUKTYWK"; // Replace with actual UID or session ID logic

    // Add domain and subdomain to formData
    const domain = "AI"; // Replace with actual domain value
    const subdomain = "ai"; // Replace with actual subdomain value

    // Prepare the data to send
    const updatedFormData = {
      username: formData.email,
      mobileno: Number(formData.mobile), // Convert to number here
      name: formData.username,
      email: formData.email,
      password: formData.password,
      domain,
      subdomain,
    };

    console.log('updatedForm', updatedFormData);

    try {
      // First API call: Wsignup/web
      const response = await fetch(`${baseUrl}/signupnew`, {
        method: "POST",
        headers: {
          "xxxid": uid,  // Custom header with the unique ID
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedFormData),  // Send updated formData with domain and subdomain
      });

      const result = await response.json();
      // if (!response.ok) {
      //   const errorMessage = result.errorMsg || result.message || "Signup failed. Please try again.";
      //   setMessage(errorMessage);
      //   setMessageType("error");
      //   setTimeout(() => setMessage(""), 2000);

      //   return;
      // }

      if (!response.ok) {
      let errorMessage = result.errorMsg || result.message || "Signup failed. Please try again.";

      // Show custom message only for this exact error condition
      if (result.errorCode === "104" && result.errorMsg === "User name already exist.") {
      errorMessage = "User Email already exists";
      }

      setMessage(errorMessage);
      setMessageType("error");
      setTimeout(() => setMessage(""), 2000);
      return;
      }


      setMessage("Signup successful!");
      setMessageType("success");
      setTimeout(() => setMessage(""), 2000);

      // Parse the response and extract user ID
      const responseData = JSON.parse(result.source);
      const userId = responseData._id?.$oid;

      if (!userId) {
        throw new Error("userId is missing or malformed in the response");
      }

      console.log("User ID from Wsignup response:", userId);

      // Second API call: eCreate (using the userId)
      const createResponse = await fetch(`${baseUrl}/eCreate?userId=${userId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "xxxid": uid, // Custom header
        },
        body: JSON.stringify(formData),
      });

      if (!createResponse.ok) {
        throw new Error("Failed to submit registration to eCreate");
      }

      // Optionally reset the form or perform other actions
      setFormData({
        username: "",
        mobile: "",
        email: "",
        password: "",
      });

      // Handle success (maybe redirect or show a success message)
      console.log("eCreate API call was successful.");

    } catch (error) {
      setMessage("An error occurred. Please try again.");
      setTimeout(() => setMessage(""), 2000);

      console.error("Error during signup:", error);
    } finally {
      setLoading(false);
      // Optionally reset the form or perform other actions
      setFormData({
        username: "",
        mobile: "",
        email: "",
        password: "",
      });
    }
  };

  return (
    <div className="flex justify-between text-n500 dark:text-n30 xxl:gap-20 max-xxl:container xxl:h-dvh max-xxl:justify-center relative ">
      {/* <GradientBackground /> */}
      <div className="py-6 xxl:ml-[calc((100%-1296px)/2)] flex-1 flex flex-col justify-between items-start max-xxl:max-w-[600px]">
        <div className="flex justify-start items-center gap-1.5">
          <Image src={currentLogo} alt="AIQuill Logo" />
          {/* <span className="text-2xl font-semibold text-n700 dark:text-n30">
            QuerryOne
          </span> */}
        </div>

        <div className="w-full pt-4">
          <p className="text-2xl font-semibold">Let’s Get Started!</p>
          <p className="text-sm pt-4">
            Please Enter your Email Address to Start your Online Application
          </p>
          <form
            onSubmit={handleSubmit}
            className="pt-6 sm:pt-10 grid grid-cols-2 gap-4 sm:gap-6"
          >
            <div className="col-span-2">
              <label>Name</label>
              <input
                type="text"
                name="username"  // Updated to use username
                placeholder="Your username here"
                value={formData.username}
                onChange={handleChange}
                className="border rounded p-2 w-full"
              />
              {errors.username && <p className="text-sm text-red-500 mt-1">{errors.username}</p>}
            </div>
            <div className="col-span-2">
              <label>Mobile Number</label>
              <input
                type="tel"
                name="mobile"
                placeholder="Your mobile number here"
                value={formData.mobile}
                onChange={handleChange}
                className="border rounded p-2 w-full"
              />
              {errors.mobile && <p className="text-sm text-red-500 mt-1">{errors.mobile}</p>}
            </div>
            <div className="col-span-2">
              <label>Email</label>
              <input
                type="email"
                name="email"
                placeholder="Your email ID here"
                value={formData.email}
                onChange={handleChange}
                className="border rounded p-2 w-full"
              />
              {errors.email && <p className="text-sm text-red-500 mt-1">{errors.email}</p>}
            </div>
            <div className="col-span-2">
              <label>Password</label>
              <input
                type="password"
                name="password"
                placeholder="*******"
                value={formData.password}
                onChange={handleChange}
                className="border rounded p-2 w-full"
              />
              {errors.password && <p className="text-sm text-red-500 mt-1">{errors.password}</p>}
            </div>

            <p className="col-span-2 text-sm pt-2">
              Have an account?{" "}
              <Link href={"/"} className="text-errorColor font-semibold">
                Sign In
              </Link>
            </p>

            <div className="col-span-2">
              <button
                type="submit"
                className="text-sm font-medium text-white bg-primaryColor text-center py-3 px-6 rounded-full block w-full"
                disabled={loading}
              >
                {loading ? "Signing Up..." : "Sign Up"}
              </button>
            </div>
          </form>

          {message && (
            <p
              className={`col-span-2 text-sm pt-2 text-center ${messageType === "success" ? "text-green-500" : "text-red-500"
                }`}
            >
              {message}
            </p>
          )}
        </div>
        <div className="flex justify-center items-center w-full pt-4">
          <Footer />
        </div>
      </div>

      <div className="w-1/2 max-xxl:hidden max-h-dvh overflow-hidden">
        <Image src={displayImg} alt="Signup Image" className="w-full object-cover" />
      </div>
    </div>
  );
}

export default SignUp;
