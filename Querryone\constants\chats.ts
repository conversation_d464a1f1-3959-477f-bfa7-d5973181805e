export const demoChatData = [
  {
    id: "776e4525-c577-4dce-b13f-d476c6a297c9",
    title: "Generate image",
    messages: [
      {
        text: "image",
        isUser: true,
        timestamp: "2025-02-16T04:29:59.197Z",
      },
      {
        text: "data-table",
        isUser: true,
        timestamp: "2025-02-16T04:31:55.124Z",
      },

      { text: "sdfsdf", isUser: true, timestamp: "2025-02-16T04:50:57.525Z" },

      { text: "code", isUser: true, timestamp: "2025-02-16T04:51:03.253Z" },
      {
        text: {
          summary:
            "Here's the implementation I've developed based on best practices:",
          code: "function getRandomColor() {\n  const letters = '0123456789ABCDEF';\n  let color = '#';\n  for (let i = 0; i < 6; i++) {\n    color += letters[Math.floor(Math.random() * 16)];\n  }\n  return color;\n}",
          language: "javascript",
        },
        isUser: false,
        timestamp: "2025-02-16T04:51:03.254Z",
      },
    ],
  },
  {
    id: "2c178ae7-2fda-4ae8-a02c-b8381957bb1b",
    title: "Edit photo effeciently",
    messages: [
      {
        text: "retouch",
        isUser: true,
        timestamp: "2025-02-16T05:03:17.038Z",
      },
      {
        text: "Let's explore your time travel story idea. Consider starting with the protagonist's initial discovery of time travel and how it affects their personal relationships. What specific era would your character visit first?",
        isUser: false,
        timestamp: "2025-02-16T05:03:17.039Z",
      },
    ],
  },
  {
    id: "98610994-9a4b-4ba4-a659-6bf004cddad8",
    title: "Make a video of artifical bot",
    messages: [
      {
        text: "video",
        isUser: true,
        timestamp: "2025-02-16T05:03:43.341Z",
      },
      {
        text: "To address urban traffic congestion, we should consider multiple approaches like improving public transportation, implementing smart traffic systems, and encouraging alternative transportation methods. Which aspect would you like to explore first?",
        isUser: false,
        timestamp: "2025-02-16T05:03:43.341Z",
      },
    ],
  },
  {
    id: "8c9aab22-063a-4811-8c84-fe8e4714b168",
    title: "Audio Content Creation",
    messages: [
      {
        text: "audio",
        isUser: true,
        timestamp: "2025-02-16T05:05:54.421Z",
      },

      { text: "image", isUser: true, timestamp: "2025-02-16T05:06:42.150Z" },
      {
        text: {
          summary:
            "I've created a visual representation based on the latest design trends:",
          image: "/assets/images/image-generation/image-generation2.jpg",
        },
        isUser: false,
        timestamp: "2025-02-16T05:06:42.150Z",
      },
    ],
  },

  {
    id: "cf0343cb-ecbf-41e0-a186-59e438fd3f06",
    title: "Write code for me",
    messages: [
      {
        text: "code",
        isUser: true,
        timestamp: "2025-02-17T04:24:32.561Z",
      },
      {
        text: "Let's explore your time travel story idea. Consider starting with the protagonist's initial discovery of time travel and how it affects their personal relationships. What specific era would your character visit first?",
        isUser: false,
        timestamp: "2025-02-17T04:24:32.562Z",
      },
    ],
  },

  {
    id: "9f507996-dd96-4c0d-bba4-2139caf84af8",
    title: "Write a short story about a time traveler who acci...",
    messages: [
      {
        text: "Write a short story about a time traveler who accidentally changes history.",
        isUser: true,
        timestamp: "2025-02-17T06:50:07.758Z",
      },
      {
        text: "Let's explore your time travel story idea. Consider starting with the protagonist's initial discovery of time travel and how it affects their personal relationships. What specific era would your character visit first?",
        isUser: false,
        timestamp: "2025-02-17T06:50:07.758Z",
      },
    ],
  },
  {
    id: "9b6ddefb-6f37-4e1d-ba8e-47809916330f",
    title: "Explain the concept of quantum entanglement in sim...",
    messages: [
      {
        text: "Explain the concept of quantum entanglement in simple terms.",
        isUser: true,
        timestamp: "2025-02-17T08:11:18.456Z",
      },
      {
        text: "Think of quantum entanglement like two dancers who always move in perfect synchronization, no matter how far apart they are. Would you like me to explain more about the basic principles or dive into specific applications?",
        isUser: false,
        timestamp: "2025-02-17T08:11:18.457Z",
      },
    ],
  },
];
