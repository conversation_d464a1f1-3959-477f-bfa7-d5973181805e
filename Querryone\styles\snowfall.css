/* styles/snowfall.css */

.logo-container {
  position: relative;
  overflow: hidden;
  background: linear-gradient(to right, #1a2231, #2c3e50);
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.logo-text {
  background: linear-gradient(to right, #4d6bfe, #6e8eff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.hexagon-logo {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hexagon-logo::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4d6bfe 0%, #2c3e50 100%);
  opacity: 0.7;
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  z-index: -1;
}

/* Snowfall animation */
@keyframes snowfall {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 0.9;
  }
  100% {
    transform: translateY(100vh) translateX(20px);
    opacity: 0;
  }
}

.snowflake {
  position: absolute;
  background-color: white;
  border-radius: 50%;
  pointer-events: none;
  animation: snowfall linear infinite;
}

/* Create different sizes and speeds for snowflakes */
.snowflake.size-1 {
  width: 2px;
  height: 2px;
  animation-duration: 10s;
}

.snowflake.size-2 {
  width: 4px;
  height: 4px;
  animation-duration: 8s;
}

.snowflake.size-3 {
  width: 6px;
  height: 6px;
  animation-duration: 6s;
}

/* Enhance logo on hover */
.logo-container:hover .logo-text {
  background: linear-gradient(to right, #6e8eff, #8aa4ff);
  -webkit-background-clip: text;
  background-clip: text;
}

.logo-container:hover .hexagon-logo::before {
  background: linear-gradient(135deg, #6e8eff 0%, #3d4f61 100%);
}
