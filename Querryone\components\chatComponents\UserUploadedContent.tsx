import React from 'react';
import { PiFile, PiFileText, PiMusicNote, PiYoutubeLogo, PiLink } from 'react-icons/pi';
import { UploadedFile, UploadedURL } from '@/stores/chatList';

interface UserUploadedContentProps {
  uploadedFiles?: UploadedFile[];
  uploadedURLs?: UploadedURL[];
  selectedLanguage: string;
}

const UserUploadedContent: React.FC<UserUploadedContentProps> = ({
  uploadedFiles = [],
  uploadedURLs = [],
  selectedLanguage
}) => {
  // Function to get language-specific text
  const getLanguageText = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return {
          uploadedFiles: 'பதிவேற்றப்பட்ட கோப்புகள்',
          uploadedUrls: 'பதிவேற்றப்பட்ட இணைப்புகள்',
          pdfDocument: 'PDF ஆவணம்',
          mp3Audio: 'MP3 ஆடியோ',
          youtubeVideo: 'YouTube வீடியோ',
          articleLink: 'கட்டுரை இணைப்பு',
          basedOn: 'அடிப்படையில்'
        };
      case "Telugu":
        return {
          uploadedFiles: 'అప్‌లోడ్ చేసిన ఫైల్‌లు',
          uploadedUrls: 'అప్‌లోడ్ చేసిన లింక్‌లు',
          pdfDocument: 'PDF డాక్యుమెంట్',
          mp3Audio: 'MP3 ఆడియో',
          youtubeVideo: 'YouTube వీడియో',
          articleLink: 'ఆర్టికల్ లింక్',
          basedOn: 'ఆధారంగా'
        };
      case "Kannada":
        return {
          uploadedFiles: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಫೈಲ್‌ಗಳು',
          uploadedUrls: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಲಿಂಕ್‌ಗಳು',
          pdfDocument: 'PDF ಡಾಕ್ಯುಮೆಂಟ್',
          mp3Audio: 'MP3 ಆಡಿಯೋ',
          youtubeVideo: 'YouTube ವೀಡಿಯೋ',
          articleLink: 'ಲೇಖನ ಲಿಂಕ್',
          basedOn: 'ಆಧಾರದ ಮೇಲೆ'
        };
      default:
        return {
          uploadedFiles: 'Uploaded Files',
          uploadedUrls: 'Uploaded URLs',
          pdfDocument: 'PDF Document',
          mp3Audio: 'MP3 Audio',
          youtubeVideo: 'YouTube Video',
          articleLink: 'Article Link',
          basedOn: 'Based on'
        };
    }
  };

  // Function to get file icon based on file type
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    if (extension === 'pdf' || extension === 'doc' || extension === 'docx') {
      return <PiFileText className="w-4 h-4" />;
    } else if (extension === 'mp3' || extension === 'wav' || extension === 'm4a') {
      return <PiMusicNote className="w-4 h-4" />;
    }
    return <PiFile className="w-4 h-4" />;
  };

  // Function to get URL icon based on type
  const getUrlIcon = (type: 'youtube' | 'article') => {
    if (type === 'youtube') {
      return <PiYoutubeLogo className="w-4 h-4" />;
    }
    return <PiLink className="w-4 h-4" />;
  };

  // Function to get language-specific color
  const getLanguageColor = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return "text-purple-600 dark:text-purple-400";
      case "Telugu":
        return "text-green-600 dark:text-green-400";
      case "Kannada":
        return "text-orange-600 dark:text-orange-400";
      default:
        return "text-blue-600 dark:text-blue-400";
    }
  };

  // Function to get background color
  const getBackgroundColor = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return "bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800";
      case "Telugu":
        return "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800";
      case "Kannada":
        return "bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800";
      default:
        return "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800";
    }
  };

  // Don't render if no uploaded content
  if ((!uploadedFiles || uploadedFiles.length === 0) && (!uploadedURLs || uploadedURLs.length === 0)) {
    return null;
  }

  const text = getLanguageText();
  const colorClass = getLanguageColor();
  const bgClass = getBackgroundColor();

  return (
    <div className={`mb-3 p-3 border rounded-lg ${bgClass}`}>
      <div className={`flex items-center gap-2 text-sm font-medium ${colorClass} mb-2`}>
        <PiFile className="w-4 h-4" />
        <span>{text.basedOn}:</span>
      </div>
      
      {/* Uploaded Files Section */}
      {uploadedFiles && uploadedFiles.length > 0 && (
        <div className="mb-2">
          <div className="space-y-1">
            {uploadedFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center gap-2 text-xs text-gray-700 dark:text-gray-300"
              >
                <div className={colorClass}>
                  {getFileIcon(file.name)}
                </div>
                <span className="truncate font-medium">
                  {file.name}
                </span>
                <span className="text-gray-500 dark:text-gray-400 text-xs">
                  ({(file.size / 1024 / 1024).toFixed(1)} MB)
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Uploaded URLs Section */}
      {uploadedURLs && uploadedURLs.length > 0 && (
        <div>
          <div className="space-y-1">
            {uploadedURLs.map((urlItem, index) => (
              <div
                key={index}
                className="flex items-center gap-2 text-xs text-gray-700 dark:text-gray-300"
              >
                <div className={colorClass}>
                  {getUrlIcon(urlItem.type)}
                </div>
                <span className="font-medium">
                  {urlItem.type === 'youtube' ? text.youtubeVideo : text.articleLink}
                </span>
                <span className="text-gray-500 dark:text-gray-400 text-xs truncate max-w-xs">
                  {urlItem.url}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default UserUploadedContent;
