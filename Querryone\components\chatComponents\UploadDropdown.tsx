import React from 'react';
import { PiFilePdf, PiYoutubeLogo, PiGlobe, PiMusicNote } from 'react-icons/pi';
import { UploadType } from './ChatInputUpload';

interface UploadDropdownProps {
  onSelect: (type: UploadType) => void;
  selectedLanguage: string;
}

interface UploadOption {
  type: UploadType;
  icon: React.ReactNode;
  label: {
    English: string;
    Tamil: string;
    Telugu: string;
    Kannada: string;
  };
  description: {
    English: string;
    Tamil: string;
    Telugu: string;
    Kannada: string;
  };
}

const uploadOptions: UploadOption[] = [
  {
    type: 'pdf',
    icon: <PiFilePdf className="w-5 h-5" />,
    label: {
      English: 'PDF/Document',
      Tamil: 'PDF/ஆவணம்',
      Telugu: 'PDF/పత్రం',
      Kannada: 'PDF/ದಾಖಲೆ'
    },
    description: {
      English: 'Upload PDF or document files',
      Tamil: 'PDF அல்லது ஆவண கோப்புகளை பதிவேற்றவும்',
      Telugu: 'PDF లేదా పత్రం ఫైల్‌లను అప్‌లోడ్ చేయండి',
      Kannada: 'PDF ಅಥವಾ ದಾಖಲೆ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'
    }
  },
  {
    type: 'youtube',
    icon: <PiYoutubeLogo className="w-5 h-5" />,
    label: {
      English: 'YouTube URL',
      Tamil: 'YouTube URL',
      Telugu: 'YouTube URL',
      Kannada: 'YouTube URL'
    },
    description: {
      English: 'Add YouTube video link',
      Tamil: 'YouTube வீடியோ இணைப்பைச் சேர்க்கவும்',
      Telugu: 'YouTube వీడియో లింక్ జోడించండి',
      Kannada: 'YouTube ವೀಡಿಯೊ ಲಿಂಕ್ ಸೇರಿಸಿ'
    }
  },
  {
    type: 'article',
    icon: <PiGlobe className="w-5 h-5" />,
    label: {
      English: 'Article URL',
      Tamil: 'கட்டுரை URL',
      Telugu: 'వ్యాసం URL',
      Kannada: 'ಲೇಖನ URL'
    },
    description: {
      English: 'Add article or webpage link',
      Tamil: 'கட்டுரை அல்லது வலைப்பக்க இணைப்பைச் சேர்க்கவும்',
      Telugu: 'వ్యాసం లేదా వెబ్‌పేజీ లింక్ జోడించండి',
      Kannada: 'ಲೇಖನ ಅಥವಾ ವೆಬ್‌ಪುಟದ ಲಿಂಕ್ ಸೇರಿಸಿ'
    }
  },
  {
    type: 'mp3',
    icon: <PiMusicNote className="w-5 h-5" />,
    label: {
      English: 'MP3 Audio',
      Tamil: 'MP3 ஆடியோ',
      Telugu: 'MP3 ఆడియో',
      Kannada: 'MP3 ಆಡಿಯೊ'
    },
    description: {
      English: 'Upload audio files',
      Tamil: 'ஆடியோ கோப்புகளை பதிவேற்றவும்',
      Telugu: 'ఆడియో ఫైల్‌లను అప్‌లోడ్ చేయండి',
      Kannada: 'ಆಡಿಯೊ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'
    }
  }
];

const UploadDropdown: React.FC<UploadDropdownProps> = ({ onSelect, selectedLanguage }) => {
  const getLanguageKey = () => {
    switch (selectedLanguage) {
      case 'Tamil':
        return 'Tamil';
      case 'Telugu':
        return 'Telugu';
      case 'Kannada':
        return 'Kannada';
      default:
        return 'English';
    }
  };

  const getHoverColor = () => {
    switch (selectedLanguage) {
      case 'Tamil':
        return 'hover:bg-purple-50 hover:border-purple-200';
      case 'Telugu':
        return 'hover:bg-green-50 hover:border-green-200';
      case 'Kannada':
        return 'hover:bg-orange-50 hover:border-orange-200';
      default:
        return 'hover:bg-blue-50 hover:border-blue-200';
    }
  };

  const getIconColor = (index: number) => {
    const colors = {
      Tamil: ['text-purple-600', 'text-purple-500', 'text-purple-700', 'text-purple-400'],
      Telugu: ['text-green-600', 'text-green-500', 'text-green-700', 'text-green-400'],
      Kannada: ['text-orange-600', 'text-orange-500', 'text-orange-700', 'text-orange-400'],
      English: ['text-blue-600', 'text-blue-500', 'text-blue-700', 'text-blue-400']
    };

    const languageColors = colors[selectedLanguage as keyof typeof colors] || colors.English;
    return languageColors[index % languageColors.length];
  };

  const languageKey = getLanguageKey();

  return (
    <div className="absolute bottom-full right-0 mb-2 w-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 shadow-lg z-50 overflow-hidden animate-fadeIn">
      <div className="p-2">
        {uploadOptions.map((option, index) => (
          <button
            key={option.type}
            onClick={() => onSelect(option.type)}
            className={`w-full flex items-start gap-3 p-3 rounded-lg border border-transparent transition-all ${getHoverColor()}`}
          >
            <div className={`flex-shrink-0 ${getIconColor(index)}`}>
              {option.icon}
            </div>
            <div className="flex-grow text-left">
              <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                {option.label[languageKey as keyof typeof option.label]}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                {option.description[languageKey as keyof typeof option.description]}
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default UploadDropdown;
