/* File Upload Component Styles */
:root {
  --bg-light: #f9f9f9;
  --bg-dark: #1a1a1a;
  --bg-dark-secondary: #2a2a2a;
  --text-primary: #333;
  --text-primary-dark: #e0e0e0;
  --text-secondary: #666;
  --text-secondary-dark: #a0a0a0;
  --border-light: #eee;
  --border-dark: #333;
  --primary-color: #4a90e2;
  --primary-color-dark: #3a7bc8;
  --primary-color-rgb: 74, 144, 226;
  --success-color: #4CAF50;
  --success-color-dark: #3d8b40;
  --error-color: #F44336;
  --error-color-dark: #d32f2f;
}

.file-upload-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.drop-area {
  position: relative;
  min-height: 200px;
  transition: all 0.3s ease;
}

.drop-area.dragging {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(var(--primary-color-rgb), 0.2);
}

.drop-icon {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.file-restriction-notice {
  display: flex;
  align-items: center;
  gap: 5px;
}

.restriction-icon {
  color: var(--primary-color);
}

.file-types {
  margin-top: 10px;
  color: var(--text-secondary);
}

.file-list {
  margin-top: 20px;
  border-radius: 8px;
  padding: 16px;
}

.dark .file-list {
  background-color: var(--bg-dark);
  color: var(--text-primary-dark);
}

.file-item {
  margin-bottom: 10px;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 12px;
  background-color: white;
  border: 1px solid #eee;
}

.dark .file-item {
  background-color: var(--bg-dark-secondary);
  color: var(--text-primary-dark);
  border-color: var(--border-dark);
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark .file-info .file-name {
  color: var(--text-primary-dark);
}

.dark .file-info .file-size {
  color: var(--text-secondary-dark);
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 5px;
}

.upload-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.dark .upload-btn {
  background-color: var(--primary-color-dark);
}

.upload-btn:hover {
  background-color: var(--primary-color-dark);
}

.dark .upload-btn:hover {
  background-color: rgba(var(--primary-color-rgb), 0.8);
}

.remove-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: color 0.3s;
}

.dark .remove-btn {
  color: var(--text-secondary-dark);
}

.remove-btn:hover {
  color: var(--error-color);
}

.dark .remove-btn:hover {
  color: var(--error-color-dark);
}

.progress-container {
  width: 100%;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
  margin: 5px 0;
}

.dark .progress-container {
  background-color: #444;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.dark .progress-bar {
  background-color: var(--primary-color-dark);
}

.progress-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.dark .progress-text {
  color: var(--text-secondary-dark);
}

.success-icon {
  color: var(--success-color);
}

.dark .success-icon {
  color: var(--success-color-dark);
}

.error-icon {
  color: var(--error-color);
}

.dark .error-icon {
  color: var(--error-color-dark);
}

.error-message {
  color: var(--error-color);
  font-size: 12px;
  margin-top: 5px;
}

.dark .error-message {
  color: var(--error-color-dark);
}

.upload-single-btn {
  margin-top: 10px;
  width: 100%;
  padding: 8px 0;
}

/* File Upload Page Styles */

.file-upload-page {
  padding: 40px 20px;
}

.file-upload-header {
  text-align: center;
  margin-bottom: 30px;
}

.upload-restriction-badge {
  display: inline-block;
  padding: 5px 15px;
  border-radius: 20px;
  margin-top: 10px;
}

.api-key-section,
.file-upload-section {
  margin-bottom: 20px;
}

.api-key-container {
  padding: 20px;
  border-radius: 8px;
}

.api-key-label {
  margin-bottom: 10px;
  display: block;
}

.api-key-input {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin-bottom: 5px;
}

.client-selection {
  margin-top: 20px;
}

.client-dropdown {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.submit-button {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.upload-status-container {
  margin-top: 30px;
  border-radius: 8px;
  background-color: var(--bg-light);
  color: var(--text-primary);
}

.dark .upload-status-container {
  background-color: var(--bg-dark);
  color: var(--text-primary-dark);
  border: 1px solid var(--border-dark);
}

.upload-status-item {
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
  background-color: white;
}

.dark .upload-status-item {
  background-color: var(--bg-dark-secondary);
  color: var(--text-primary-dark);
}

.upload-status-item.success {
  border-left: 4px solid var(--success-color);
}

.upload-status-item.error {
  border-left: 4px solid var(--error-color);
}

.upload-status-item.uploading {
  border-left: 4px solid var(--primary-color);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.dark .status-header {
  color: var(--text-primary-dark);
}

.file-status {
  display: flex;
  align-items: center;
}

.uploading {
  display: flex;
  align-items: center;
  color: var(--primary-color);
}

.dark .uploading {
  color: var(--primary-color-dark, #4a90e2);
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(var(--primary-color-rgb), 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.dark .spinner {
  border: 2px solid rgba(var(--primary-color-rgb, 74, 144, 226), 0.2);
  border-top-color: var(--primary-color-dark, #4a90e2);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.status-message {
  margin: 10px 0;
  font-size: 14px;
}

.dark .status-message {
  color: var(--text-secondary-dark, #a0a0a0);
}

.processing-stages {
  display: flex;
  margin: 20px 0;
  position: relative;
}

.stage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.stage-indicator {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  z-index: 2;
  transition: all 0.3s ease;
}

.dark .stage-indicator {
  background-color: #2a2a2a;
  color: #e0e0e0;
}

.stage-indicator.active {
  background-color: var(--primary-color);
  color: white;
}

.dark .stage-indicator.active {
  background-color: var(--primary-color-dark, #4a90e2);
  color: white;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}

.stage-indicator.complete {
  background-color: var(--success-color);
  color: white;
}

.dark .stage-indicator.complete {
  background-color: var(--success-color-dark, #3d8b40);
  color: white;
}

.stage-label {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 5px;
}

.dark .stage-label {
  color: var(--text-primary-dark, #e0e0e0);
}

.stage-description {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

.dark .stage-description {
  color: var(--text-secondary-dark, #a0a0a0);
}

.upload-details {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.dark .upload-details {
  border-top: 1px solid #333;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.dark .detail-item {
  color: var(--text-primary-dark, #e0e0e0);
}

.detail-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.dark .detail-label {
  color: var(--text-secondary-dark, #a0a0a0);
}

.dark .detail-value {
  color: var(--text-primary-dark, #e0e0e0);
}

@media (max-width: 768px) {
  .processing-stages {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .stage-item {
    flex-direction: row;
    align-items: center;
    width: 100%;
  }

  .stage-indicator {
    margin-right: 15px;
    margin-bottom: 0;
  }

  .stage-info {
    text-align: left;
  }
}
