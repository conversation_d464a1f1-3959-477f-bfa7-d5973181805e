// components/chatComponents/ChatMessages.tsx
import React, { useEffect, useRef, useState } from "react";
import { useChatHandler } from "@/stores/chatList";
import BotReply from "./BotReply";
import UserMessage from "./UserMessage";
import LoadingIndicator from "./LoadingIndicator";

type ChatMessagesProps = {
  chatId: string;
  onSelectQuestion?: (question: string) => void;
};

function ChatMessages({ chatId, onSelectQuestion }: ChatMessagesProps) {
  const { chatList, isLoading } = useChatHandler();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [shouldScroll, setShouldScroll] = useState<boolean>(true);
  const [selectedLanguage, setSelectedLanguage] = useState("English");

  // Handle selecting a related question
  const handleSelectQuestion = (question: string) => {
    console.log("Selected related question:", question);
    // Call the parent's onSelectQuestion function if provided
    if (onSelectQuestion) {
      onSelectQuestion(question);
    }
  };

  // Find the current chat
  const currentChat = chatList.find((chat) => chat.id === chatId);

  // Enhanced debug log
  useEffect(() => {
    if (currentChat) {
      console.log("Current chat messages:", currentChat.messages);
      console.log("ChatMessages: chatList state:", chatList);

      // Log each message in detail
      currentChat.messages.forEach((msg, idx) => {
        console.log(`Message ${idx} details:`, {
          isUser: msg.isUser,
          textType: typeof msg.text,
          textValue: msg.text,
          textLength: typeof msg.text === 'string' ? msg.text.length : 'N/A',
          textSample: typeof msg.text === 'string'
            ? msg.text.substring(0, 100) + (msg.text.length > 100 ? '...' : '')
            : JSON.stringify(msg.text).substring(0, 100) + '...',
          timestamp: msg.timestamp
        });
      });
    } else {
      console.warn("Chat not found with ID:", chatId);
    }
  }, [currentChat, chatId, chatList]);

  // Scroll to bottom when new messages arrive or loading state changes
  useEffect(() => {
    if (shouldScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      // Reset shouldScroll after scrolling
      setTimeout(() => setShouldScroll(false), 100);
    }
  }, [currentChat?.messages.length, isLoading, shouldScroll]);

  // Ensure we trigger scroll when new messages are added
  useEffect(() => {
    if (currentChat?.messages.length) {
      setShouldScroll(true);
    }
  }, [currentChat?.messages.length]);

  // Helper function to process message content for display
  const processMessageContent = (messageContent: any): string => {
    console.log("processMessageContent received:", messageContent);

    if (messageContent === null || messageContent === undefined) {
      console.warn("Message content is null or undefined");
      return "No message content available";
    }

    // If it's already a string (which is the expected format from the API)
    if (typeof messageContent === 'string') {
      console.log("Message content is a string of length:", messageContent.length);

      // Check if it's a JSON string that needs parsing
      if (messageContent.trim().startsWith('{') && messageContent.trim().endsWith('}')) {
        try {
          const parsed = JSON.parse(messageContent);
          console.log("Successfully parsed JSON message:", parsed);

          // If the parsed object has an ai_response property, extract it
          if (parsed.ai_response) {
            console.log("Found ai_response in parsed JSON, using that");
            const response = parsed.ai_response;
            return typeof response === 'string' ? response : JSON.stringify(response);
          }

          // If it has a text property
          if (parsed.text) {
            console.log("Found text property in parsed JSON");
            const text = parsed.text;
            return typeof text === 'string' ? text : JSON.stringify(text);
          }

          // Return the parsed object as a string
          return JSON.stringify(parsed);
        } catch (e) {
          console.log("Not valid JSON, keeping as string");
        }
      }

      // Regular string, return as is
      return messageContent;
    }

    // If it's an object, check if it has an ai_response property
    if (typeof messageContent === 'object' && messageContent !== null) {
      console.log("Message content is an object with keys:", Object.keys(messageContent));

      if ('ai_response' in messageContent) {
        console.log("Found ai_response property in object");
        const response = messageContent.ai_response;
        return typeof response === 'string' ? response : JSON.stringify(response);
      }

      // If it has a text property
      if ('text' in messageContent) {
        console.log("Found text property in object");
        const text = messageContent.text;
        return typeof text === 'string' ? text : JSON.stringify(text);
      }
    }

    // For any other case, try to convert to string
    console.log("Converting message content to string, type:", typeof messageContent);
    try {
      return typeof messageContent === 'object'
        ? JSON.stringify(messageContent, null, 2)
        : String(messageContent);
    } catch (e) {
      console.error("Failed to convert message content to string:", e);
      return "Error: Could not process this message content.";
    }
  };

  if (!currentChat) {
    return (
      <div className="flex justify-center items-center h-full py-10">
        <p className="text-n100">Chat not found or loading...</p>
      </div>
    );
  }

  // Add a debugging component to see the raw message data
  const DebugMessages = () => {
    if (process.env.NODE_ENV !== 'development') return null;

    return (
      <div className="bg-gray-100 p-4 mb-4 rounded text-xs overflow-auto max-h-[200px]">
        <h3 className="font-bold mb-2">Debug: Chat Messages</h3>
        {currentChat.messages.map((msg, idx) => (
          <div key={idx} className="mb-2 border-b pb-2">
            <p><strong>Message {idx}:</strong> {msg.isUser ? 'User' : 'Bot'}</p>
            <p><strong>Type:</strong> {typeof msg.text}</p>
            <p><strong>Content:</strong> {
              typeof msg.text === 'string'
                ? msg.text.substring(0, 100) + (msg.text.length > 100 ? '...' : '')
                : JSON.stringify(msg.text).substring(0, 100) + '...'
            }</p>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="flex flex-col gap-6 py-6">
      {/* Debug component */}
      {process.env.NODE_ENV === 'development' && <DebugMessages />}

      {/* This welcome message was moved down to be shown only when no messages and not loading */}

      {/* Render all messages */}
      {currentChat.messages.map((message, index) => {
        // Enhanced debugging for message content
        console.log(`Rendering message ${index}:`, {
          isUser: message.isUser,
          contentType: typeof message.text,
          contentValue: message.text,
          contentSample: typeof message.text === 'string'
            ? message.text.substring(0, 50) + (message.text.length > 50 ? '...' : '')
            : JSON.stringify(message.text).substring(0, 50) + '...'
        });

        if (message.isUser) {
          // For user messages
          const userMessageText = typeof message.text === 'string'
            ? message.text
            : typeof message.text === 'object' && message.text && 'summary' in message.text
              ? message.text.summary
              : JSON.stringify(message.text);

          return (
            <UserMessage
              key={`user-${index}`}
              message={userMessageText}
              timestamp={message.timestamp}
              uploadedFiles={message.uploadedFiles}
              uploadedURLs={message.uploadedURLs}
              selectedLanguage={selectedLanguage}
            />
          );
        } else {
          // For bot messages
          console.log(`Processing bot message ${index}:`, message);

          // Check if this is a loading message
          if (message.text === "__LOADING__") {
            return (
              <LoadingIndicator
                key={`loading-${index}`}
                message="Generating response"
                showSteps={true}
                showPulse={true}
                timestamp={message.timestamp}
              />
            );
          }

          // Make sure we have valid content to display
          if (message.text === undefined || message.text === null) {
            console.error("Bot message has null/undefined text content");
            return (
              <BotReply
                key={`bot-${index}`}
                replyType="response"
                isAnimation={false}
                setScroll={setShouldScroll}
                aiResponse="Sorry, there was an issue with this response."
                timestamp={message.timestamp}
                selectedLanguage={selectedLanguage}
                onSelectQuestion={handleSelectQuestion}
              />
            );
          }

          // Log the message text for debugging
          console.log(`Bot message ${index} text:`, {
            type: typeof message.text,
            value: message.text,
            sample: typeof message.text === 'string'
              ? message.text.substring(0, 50) + (message.text.length > 50 ? '...' : '')
              : JSON.stringify(message.text).substring(0, 50) + '...'
          });

          // Process the message text to ensure it's in the right format for BotReply
          console.log(`Bot message ${index} before processing:`, {
            type: typeof message.text,
            value: message.text,
            sample: typeof message.text === 'string'
              ? message.text.substring(0, 100) + (message.text.length > 100 ? '...' : '')
              : JSON.stringify(message.text).substring(0, 100) + '...'
          });

          // Check if message.text is an object with ai_response and sentence_analysis
          let aiResponseData = message.text;

          // Generate a unique message ID based on timestamp and index
          const messageId = `msg-${message.timestamp}-${index}`;

          // Log detailed information about the message data
          console.log(`Bot message ${index} data to pass to BotReply:`, {
            type: typeof aiResponseData,
            keys: typeof aiResponseData === 'object' ? Object.keys(aiResponseData) : 'N/A',
            hasSentenceAnalysis: typeof aiResponseData === 'object' &&
              aiResponseData !== null &&
              'sentence_analysis' in aiResponseData &&
              Array.isArray(aiResponseData.sentence_analysis)
          });

          // Log sentence analysis data if available
          if (typeof aiResponseData === 'object' &&
              aiResponseData !== null &&
              'sentence_analysis' in aiResponseData &&
              Array.isArray(aiResponseData.sentence_analysis)) {
            console.log(`Bot message ${index} sentence analysis:`, aiResponseData.sentence_analysis);
          }

          // Pass the complete message data to BotReply with a unique messageId
          // The animation will be handled inside BotReply based on the messageId
          return (
            <BotReply
              key={`bot-${index}`}
              replyType="response"
              isAnimation={false}
              setScroll={setShouldScroll}
              aiResponse={aiResponseData}
              timestamp={message.timestamp}
              messageId={messageId}
              selectedLanguage={selectedLanguage}
              onSelectQuestion={handleSelectQuestion}
            />
          );
        }
      })}

      {/* Show loading animation when waiting for AI response */}
      {isLoading && (
        <LoadingIndicator
          message="Generating answers for you"
          showSteps={true}
          showPulse={true}
          timestamp={new Date().toISOString()}
        />
      )}

      {/* Show message when no messages and not loading */}
      {currentChat.messages.length === 0 && !isLoading && (
        <div className="text-center py-10">
          <h3 className="text-lg font-medium mb-2">Start a new conversation</h3>
          <p className="text-n100">Type a message below to chat with QueryOne</p>
        </div>
      )}

      {/* Scroll anchor for auto-scroll */}
      <div ref={messagesEndRef} />
    </div>
  );
}

export default ChatMessages;