"""
Enhanced upload endpoints with async processing, progress tracking, and better error handling.
This module provides improved document processing capabilities for the Flask application.
"""

import os
import uuid
import asyncio
import threading
from flask import request, jsonify
from typing import Dict, Any, Optional
import tempfile
import time

# Import processors
try:
    from .pdf_processor import process_pdf_file, get_progress as get_pdf_progress, update_progress
    from .document_processor import process_document_file
    from .article_processor import process_article_url, extract_article_content_enhanced
    from .youtube_processor import process_youtube_url
    from .cache_manager import get_cache_manager
except ImportError:
    # Fallback imports for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from services.pdf_processor import process_pdf_file, get_progress as get_pdf_progress, update_progress
    from services.document_processor import process_document_file
    from services.article_processor import process_article_url, extract_article_content_enhanced
    from services.youtube_processor import process_youtube_url
    from services.cache_manager import get_cache_manager

# Global progress tracking
upload_progress = {}
progress_lock = threading.Lock()

def update_upload_progress(upload_id: str, progress: int, message: str = "", status: str = "processing"):
    """Update upload progress with thread safety"""
    with progress_lock:
        upload_progress[upload_id] = {
            'progress': progress,
            'message': message,
            'status': status,
            'timestamp': time.time()
        }

def get_upload_progress(upload_id: str) -> Dict[str, Any]:
    """Get upload progress"""
    with progress_lock:
        return upload_progress.get(upload_id, {
            'progress': 0,
            'message': 'Upload not found',
            'status': 'not_found'
        })

def cleanup_old_progress():
    """Clean up old progress entries (older than 1 hour)"""
    current_time = time.time()
    with progress_lock:
        expired_keys = [
            key for key, value in upload_progress.items()
            if current_time - value.get('timestamp', 0) > 3600
        ]
        for key in expired_keys:
            del upload_progress[key]

def process_pdf_with_progress(file_content: bytes, filename: str, index_name: str, upload_id: str):
    """Process PDF with progress tracking and caching"""
    try:
        cache_manager = get_cache_manager()

        # Generate file hash for caching
        file_hash = cache_manager.generate_file_hash(file_content, filename)

        # Check cache first
        cached_result = cache_manager.get_file_hash_result(file_hash)
        if cached_result:
            update_upload_progress(upload_id, 100, "PDF processing completed (from cache)!", "completed")
            return {
                "success": True,
                "message": "PDF processed successfully (from cache)",
                "cached": True,
                "file_hash": file_hash
            }

        update_upload_progress(upload_id, 10, "Starting PDF processing...")

        # Use the enhanced PDF processor
        success = process_pdf_file(
            file_content=file_content,
            original_filename=filename,
            index_name=index_name,
            process_id=upload_id
        )

        if success:
            result = {
                "success": True,
                "message": "PDF processed successfully",
                "file_hash": file_hash,
                "filename": filename,
                "index_name": index_name
            }

            # Cache the successful result
            cache_manager.cache_file_hash_result(file_hash, result)

            update_upload_progress(upload_id, 100, "PDF processing completed successfully!", "completed")
            return result
        else:
            update_upload_progress(upload_id, 0, "PDF processing failed", "failed")
            return {"success": False, "error": "PDF processing failed"}

    except Exception as e:
        error_msg = f"Error processing PDF: {str(e)}"
        update_upload_progress(upload_id, 0, error_msg, "failed")
        return {"success": False, "error": error_msg}

def process_document_with_progress(file_path: str, filename: str, index_name: str, upload_id: str):
    """Process document with progress tracking"""
    try:
        update_upload_progress(upload_id, 10, "Starting document processing...")
        
        # Use the document processor
        success = process_document_file(
            file_path=file_path,
            original_filename=filename,
            index_name=index_name
        )
        
        if success:
            update_upload_progress(upload_id, 100, "Document processing completed successfully!", "completed")
            return {"success": True, "message": "Document processed successfully"}
        else:
            update_upload_progress(upload_id, 0, "Document processing failed", "failed")
            return {"success": False, "error": "Document processing failed"}
            
    except Exception as e:
        error_msg = f"Error processing document: {str(e)}"
        update_upload_progress(upload_id, 0, error_msg, "failed")
        return {"success": False, "error": error_msg}

def process_url_with_progress(url: str, url_type: str, index_name: str, upload_id: str):
    """Process URL with progress tracking and caching"""
    try:
        cache_manager = get_cache_manager()

        # Generate cache key for URL processing
        url_hash = cache_manager.generate_file_hash(url.encode('utf-8'), f"{url_type}_{index_name}")

        # Check cache first
        cached_result = cache_manager.get_file_hash_result(url_hash)
        if cached_result:
            update_upload_progress(upload_id, 100, f"{url_type.title()} processing completed (from cache)!", "completed")
            return {
                "success": True,
                "message": f"{url_type.title()} processed successfully (from cache)",
                "cached": True,
                "url_hash": url_hash
            }

        update_upload_progress(upload_id, 10, f"Starting {url_type} processing...")

        if url_type == "article":
            success = process_article_url(url, index_name=index_name)
        elif url_type == "youtube":
            success = process_youtube_url(url, index_name=index_name)
        else:
            raise ValueError(f"Unsupported URL type: {url_type}")

        if success:
            result = {
                "success": True,
                "message": f"{url_type.title()} processed successfully",
                "url_hash": url_hash,
                "url": url,
                "url_type": url_type,
                "index_name": index_name
            }

            # Cache the successful result
            cache_manager.cache_file_hash_result(url_hash, result)

            update_upload_progress(upload_id, 100, f"{url_type.title()} processing completed successfully!", "completed")
            return result
        else:
            update_upload_progress(upload_id, 0, f"{url_type.title()} processing failed", "failed")
            return {"success": False, "error": f"{url_type.title()} processing failed"}

    except Exception as e:
        error_msg = f"Error processing {url_type}: {str(e)}"
        update_upload_progress(upload_id, 0, error_msg, "failed")
        return {"success": False, "error": error_msg}

def create_enhanced_upload_endpoints(app):
    """Create enhanced upload endpoints for the Flask app"""
    
    @app.route('/api/enhanced/upload-pdf', methods=['POST'])
    def enhanced_upload_pdf():
        """Enhanced PDF upload with progress tracking"""
        try:
            # Validate file upload
            if 'file' not in request.files:
                return jsonify({'success': False, 'error': 'No file provided'}), 400

            file = request.files['file']
            if file.filename == '':
                return jsonify({'success': False, 'error': 'No file selected'}), 400

            # Get parameters
            index_name = request.form.get('index_name', 'default')
            client_email = request.form.get('client_email', '')

            # Validate file type
            if not file.filename.lower().endswith('.pdf'):
                return jsonify({'success': False, 'error': 'Only PDF files are supported'}), 400

            # Generate upload ID
            upload_id = str(uuid.uuid4())

            # Read file content
            file_content = file.read()
            if not file_content:
                return jsonify({'success': False, 'error': 'Empty file'}), 400

            # Start processing in background thread
            def background_process():
                result = process_pdf_with_progress(file_content, file.filename, index_name, upload_id)
                
            thread = threading.Thread(target=background_process)
            thread.daemon = True
            thread.start()

            return jsonify({
                'success': True,
                'upload_id': upload_id,
                'message': 'PDF upload started',
                'filename': file.filename,
                'index_name': index_name
            })

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/enhanced/upload-document', methods=['POST'])
    def enhanced_upload_document():
        """Enhanced document upload with progress tracking"""
        try:
            # Validate file upload
            if 'file' not in request.files:
                return jsonify({'success': False, 'error': 'No file provided'}), 400

            file = request.files['file']
            if file.filename == '':
                return jsonify({'success': False, 'error': 'No file selected'}), 400

            # Get parameters
            index_name = request.form.get('index_name', 'default')
            client_email = request.form.get('client_email', '')

            # Validate file type
            supported_extensions = {'.doc', '.docx', '.txt', '.rtf', '.md', '.html', '.htm'}
            file_ext = os.path.splitext(file.filename)[1].lower()
            if file_ext not in supported_extensions:
                return jsonify({
                    'success': False,
                    'error': f'Unsupported file type: {file_ext}. Supported: {list(supported_extensions)}'
                }), 400

            # Generate upload ID
            upload_id = str(uuid.uuid4())

            # Save file temporarily
            temp_dir = tempfile.mkdtemp()
            temp_path = os.path.join(temp_dir, file.filename)
            file.save(temp_path)

            # Start processing in background thread
            def background_process():
                try:
                    result = process_document_with_progress(temp_path, file.filename, index_name, upload_id)
                finally:
                    # Clean up temporary file
                    try:
                        os.remove(temp_path)
                        os.rmdir(temp_dir)
                    except:
                        pass
                
            thread = threading.Thread(target=background_process)
            thread.daemon = True
            thread.start()

            return jsonify({
                'success': True,
                'upload_id': upload_id,
                'message': 'Document upload started',
                'filename': file.filename,
                'index_name': index_name
            })

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/enhanced/process-url', methods=['POST'])
    def enhanced_process_url():
        """Enhanced URL processing with progress tracking"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            url = data.get('url', '').strip()
            url_type = data.get('type', '').lower()  # 'article' or 'youtube'
            index_name = data.get('index_name', 'default')
            client_email = data.get('client_email', '')

            if not url:
                return jsonify({'success': False, 'error': 'URL is required'}), 400

            if url_type not in ['article', 'youtube']:
                return jsonify({'success': False, 'error': 'URL type must be "article" or "youtube"'}), 400

            # Generate upload ID
            upload_id = str(uuid.uuid4())

            # Start processing in background thread
            def background_process():
                result = process_url_with_progress(url, url_type, index_name, upload_id)
                
            thread = threading.Thread(target=background_process)
            thread.daemon = True
            thread.start()

            return jsonify({
                'success': True,
                'upload_id': upload_id,
                'message': f'{url_type.title()} processing started',
                'url': url,
                'type': url_type,
                'index_name': index_name
            })

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/enhanced/upload-progress/<upload_id>', methods=['GET'])
    def get_enhanced_upload_progress(upload_id):
        """Get upload progress"""
        try:
            progress_data = get_upload_progress(upload_id)
            return jsonify({
                'success': True,
                'upload_id': upload_id,
                'progress': progress_data
            })
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/enhanced/cleanup-progress', methods=['POST'])
    def cleanup_progress():
        """Clean up old progress entries"""
        try:
            cleanup_old_progress()
            return jsonify({'success': True, 'message': 'Progress cleanup completed'})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/enhanced/cache-stats', methods=['GET'])
    def get_cache_stats():
        """Get cache statistics"""
        try:
            cache_manager = get_cache_manager()
            stats = cache_manager.get_all_stats()
            return jsonify({
                'success': True,
                'cache_stats': stats
            })
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/enhanced/cache-clear', methods=['POST'])
    def clear_cache():
        """Clear all caches"""
        try:
            cache_manager = get_cache_manager()
            cache_manager.clear_all_caches()
            return jsonify({'success': True, 'message': 'All caches cleared successfully'})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/enhanced/cache-toggle', methods=['POST'])
    def toggle_cache():
        """Toggle cache functionality"""
        try:
            data = request.get_json()
            enabled = data.get('enabled', True) if data else True

            cache_manager = get_cache_manager()
            cache_manager.toggle_cache(enabled)

            return jsonify({
                'success': True,
                'message': f'Cache {"enabled" if enabled else "disabled"}',
                'cache_enabled': enabled
            })
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    return app
