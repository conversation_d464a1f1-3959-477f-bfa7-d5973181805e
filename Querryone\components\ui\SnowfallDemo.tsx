import React, { useState } from 'react';
import AIQuill<PERSON>ogo from './AIQuillLogo';
import LogoWithSnowfall from './LogoWithSnowfall';
import SimpleAIQuillLogo from './SimpleAIQuillLogo';
import AIQuillLogoWithSnowfall from './AIQuillLogoWithSnowfall';

const SnowfallDemo: React.FC = () => {
  const [showCanvas, setShowCanvas] = useState(true);
  const [darkMode, setDarkMode] = useState(true);

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-100'} p-8`}>
      <div className="max-w-4xl mx-auto">
        <h1 className={`text-3xl font-bold mb-8 ${darkMode ? 'text-white' : 'text-gray-800'}`}>
          QueryOne Logo with Snowfall Animation
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Option 1: CSS-based snowfall */}
          <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl text-white mb-4">CSS Snowfall Animation</h2>
            <div className="h-40 flex items-center justify-center">
              <AIQuillLogo showSnowfall={true} darkMode={true} />
            </div>
            <p className="text-gray-300 mt-4">
              This implementation uses CSS animations for the snowfall effect.
            </p>
          </div>

          {/* Option 2: Canvas-based snowfall */}
          <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl text-white mb-4">Canvas Snowfall Animation</h2>
            <div className="h-40 flex items-center justify-center">
              {showCanvas && <LogoWithSnowfall />}
            </div>
            <p className="text-gray-300 mt-4">
              This implementation uses Canvas for more dynamic snowfall.
            </p>
          </div>

          {/* Option 3: Simple Logo (Matches your provided code) */}
          <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl text-white mb-4">Simple Logo (No Animation)</h2>
            <div className="h-40 flex items-center justify-center">
              <SimpleAIQuillLogo darkMode={true} />
            </div>
            <p className="text-gray-300 mt-4">
              This matches your provided code structure exactly.
            </p>
          </div>

          {/* Option 4: Simple Logo with Snowfall */}
          <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl text-white mb-4">Simple Logo with Snowfall</h2>
            <div className="h-40 flex items-center justify-center">
              <AIQuillLogoWithSnowfall />
            </div>
            <p className="text-gray-300 mt-4">
              This combines your logo structure with snowfall animation.
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="mt-8 p-4 bg-gray-800 rounded-lg">
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => setShowCanvas(!showCanvas)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              {showCanvas ? 'Hide' : 'Show'} Canvas Animation
            </button>

            <button
              onClick={() => setDarkMode(!darkMode)}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Toggle {darkMode ? 'Light' : 'Dark'} Mode
            </button>
          </div>
        </div>

        {/* Usage Example */}
        <div className={`mt-8 p-6 rounded-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'}`}>
          <h2 className="text-xl font-semibold mb-4">Usage Example</h2>
          <pre className="bg-gray-900 text-gray-200 p-4 rounded overflow-x-auto">
{`// Import the component
import AIQuillLogoWithSnowfall from '@/components/ui/AIQuillLogoWithSnowfall';

// Use in your layout or component
<AIQuillLogoWithSnowfall />

// Or use the simple version without animation
import SimpleAIQuillLogo from '@/components/ui/SimpleAIQuillLogo';

<div className="flex justify-start items-center gap-1.5">
  <SimpleAIQuillLogo darkMode={true} />
</div>`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default SnowfallDemo;
