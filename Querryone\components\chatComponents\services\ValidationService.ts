export class ValidationService {
  // Function to detect if text is likely Tamil
  static isTamilText(text: string): boolean {
    // Tamil Unicode range: \u0B80-\u0BFF
    const tamilRegex = /[\u0B80-\u0BFF]/;
    return tamilRegex.test(text);
  }

  // Function to detect if text is likely Telugu
  static isTeluguText(text: string): boolean {
    // Telugu Unicode range: \u0C00-\u0C7F
    const teluguRegex = /[\u0C00-\u0C7F]/;
    return teluguRegex.test(text);
  }

  // Function to detect if text is likely Kannada
  static isKannadaText(text: string): boolean {
    // Kannada Unicode range: \u0C80-\u0CFF
    const kannadaRegex = /[\u0C80-\u0CFF]/;
    return kannadaRegex.test(text);
  }

  // Function to validate if text matches the selected language
  static validateLanguageMatch(text: string, language: string): boolean {
    if (!text || text.trim() === "") return true; // Empty text is valid for any language

    // First, remove continuous capital English words (acronyms, proper nouns, etc.)
    // These should be preserved in any language and not affect validation
    const textWithoutCapitalWords = text.replace(/\b[A-Z]{2,}\b/g, '');

    // Check if the remaining text contains characters from different languages
    const hasTamilChars = this.isTamilText(textWithoutCapitalWords);
    const hasTeluguChars = this.isTeluguText(textWithoutCapitalWords);
    const hasKannadaChars = this.isKannadaText(textWithoutCapitalWords);

    // Count how many different language scripts are present
    const scriptCount = (hasTamilChars ? 1 : 0) + (hasTeluguChars ? 1 : 0) + (hasKannadaChars ? 1 : 0);

    // If there are multiple scripts, it's likely a mismatch
    if (scriptCount > 1) {
      return false;
    }

    // English can contain any characters, so we only validate non-English languages
    if (language === "English") {
      // For English, we consider it valid if it doesn't contain Tamil, Telugu, or Kannada characters
      return !(hasTamilChars || hasTeluguChars || hasKannadaChars);
    } else if (language === "Tamil") {
      // For Tamil, it should contain Tamil characters
      return hasTamilChars || textWithoutCapitalWords.trim() === '';
    } else if (language === "Telugu") {
      // For Telugu, it should contain Telugu characters
      return hasTeluguChars || textWithoutCapitalWords.trim() === '';
    } else if (language === "Kannada") {
      // For Kannada, it should contain Kannada characters
      return hasKannadaChars || textWithoutCapitalWords.trim() === '';
    }

    return true; // Default case
  }

  // Function to detect the language of the text
  static detectLanguage(text: string): string {
    if (this.isTamilText(text)) return "Tamil";
    if (this.isTeluguText(text)) return "Telugu";
    if (this.isKannadaText(text)) return "Kannada";
    return "English";
  }
}
