import React from 'react';
import { PiArrowRight } from "react-icons/pi";

interface NewsItem {
  title?: string;
  summary?: string;
  url?: string;
  image_url?: string;
  formatted_date?: string;
  source?: string;
  category?: string;
  published?: string;
}

interface NewsCardProps {
  news: NewsItem;
}

const NewsCard: React.FC<NewsCardProps> = ({ news }) => {
  const {
    title = 'No title',
    summary = 'No summary available',
    url = '#',
    image_url,
    formatted_date,
    published,
    source = 'Unknown source',
    category = 'all'
  } = news;

  // Format the date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'No date';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      return dateString || 'No date';
    }
  };

  const displayDate = formatted_date || (published ? formatDate(published) : 'No date');
  const imageUrl = image_url || 'https://via.placeholder.com/600x400?text=No+Image';
  const truncatedSummary = summary
    ? (summary.substring(0, 120) + (summary.length > 120 ? '...' : ''))
    : 'No summary available';

  // Format category for display (capitalize first letter)
  const displayCategory = category !== 'all'
    ? category.charAt(0).toUpperCase() + category.slice(1)
    : null;

  return (
    <div className="border border-primaryColor/20 p-0 rounded-lg overflow-hidden hover:border-primaryColor/50 hover:shadow-md duration-300 cursor-pointer bg-white dark:bg-n0">
      <div
        className="h-[200px] w-full bg-cover bg-center"
        style={{ backgroundImage: `url('${imageUrl}')` }}
      ></div>
      <div className="p-5">
        <div className="flex justify-between items-center mb-3">
          <div className="text-xs text-n100 dark:text-n30">{displayDate}</div>
          <span className="text-xs bg-primaryColor/10 text-primaryColor px-2 py-1 rounded-md">{source}</span>
        </div>
        <h3 className="text-lg font-medium mb-3 line-clamp-2 dark:text-n30">{title}</h3>

        {displayCategory && (
          <div className="mb-3">
            <span className="inline-block px-2 py-1 text-xs font-medium bg-primaryColor/10 text-primaryColor rounded-full">
              {displayCategory}
            </span>
          </div>
        )}

        <p className="text-sm text-n100 dark:text-n50 mb-4 line-clamp-3">{truncatedSummary}</p>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="text-primaryColor flex items-center text-sm font-medium hover:underline"
        >
          Read more <PiArrowRight className="ml-1" />
        </a>
      </div>
    </div>
  );
};

export default NewsCard;
