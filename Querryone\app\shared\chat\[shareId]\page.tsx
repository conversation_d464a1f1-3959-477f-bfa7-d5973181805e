"use client";
import React, { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { sharingService, ShareableChat } from "@/services/sharingService";
import BotReply from "@/components/chatComponents/BotReply";
import UserMessage from "@/components/chatComponents/UserMessage";
import { PiClock, PiEye, PiLock, PiGlobe, PiWarning, PiSignIn, PiChatCircle, PiArrowRight } from "react-icons/pi";
import Image from "next/image";
import logo from "@/public/images/favicon.ico";
import ChatBox from "@/components/chatComponents/ChatBox";

function SharedChatPage() {
  const params = useParams();
  const router = useRouter();
  const shareId = params?.shareId as string;

  const [sharedChat, setSharedChat] = useState<ShareableChat | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userName, setUserName] = useState<string>("");
  const [showContinueChat, setShowContinueChat] = useState(false);

  // Check authentication status
  useEffect(() => {
    const checkAuthStatus = () => {
      const userData = sessionStorage.getItem("resultUser");
      if (userData) {
        try {
          const user = JSON.parse(userData);
          setIsLoggedIn(true);
          setUserName(user.name || "User");
        } catch (error) {
          console.error("Failed to parse user data:", error);
          setIsLoggedIn(false);
        }
      } else {
        setIsLoggedIn(false);
      }
    };

    checkAuthStatus();
  }, []);

  useEffect(() => {
    const loadSharedChat = async () => {
      if (!shareId) {
        setError("Invalid share link");
        setLoading(false);
        return;
      }

      try {
        const chat = await sharingService.getSharedChat(shareId);

        if (!chat) {
          setError("This shared conversation is no longer available. It may have expired or been removed.");
          setLoading(false);
          return;
        }

        setSharedChat(chat);
      } catch (err) {
        console.error("Failed to load shared chat:", err);
        setError("Failed to load the shared conversation. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    loadSharedChat();
  }, [shareId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primaryColor mx-auto"></div>
          <p className="text-n400">Loading shared conversation...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4 max-w-md mx-auto px-6">
          <PiWarning className="text-6xl text-red-500 mx-auto" />
          <h1 className="text-2xl font-bold text-n700 dark:text-n100">
            Conversation Not Found
          </h1>
          <p className="text-n400">{error}</p>
          <button
            onClick={() => window.location.href = '/'}
            className="px-6 py-3 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 transition-colors"
          >
            Go to AIQuill
          </button>
        </div>
      </div>
    );
  }

  if (!sharedChat) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle login redirect
  const handleLoginRedirect = () => {
    // Store the current shared chat URL to redirect back after login
    localStorage.setItem('redirectAfterLogin', window.location.href);
    router.push('/sign-in');
  };

  // Handle continue chat for logged-in users
  const handleContinueChat = async () => {
    if (!sharedChat || !isLoggedIn) return;

    try {
      // Import the shared chat into user's chat list
      const { useChatHandler } = await import("@/stores/chatList");
      const { addChat } = useChatHandler.getState();

      // Create a new chat based on the shared chat with proper structure
      const newChatId = `imported-${Date.now()}`;
      const newChat = {
        id: newChatId,
        title: `${sharedChat.title} (Continued)`,
        createdAt: new Date().toISOString(), // Use createdAt instead of timestamp
        messages: sharedChat.messages.map(msg => ({
          ...msg,
          timestamp: msg.timestamp || new Date().toISOString()
        })),
        indexUsed: 'default'
      };

      // Add to user's chat list (this will sync to server automatically)
      addChat(newChat);

      // Wait a moment for the sync to complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Redirect to the new chat
      router.push(`/chat/${newChatId}`);
    } catch (error) {
      console.error("Failed to continue chat:", error);
      // Fallback: redirect to new chat
      router.push('/new-chat');
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-n0">
      {/* Header */}
      <div className="border-b border-primaryColor/10 bg-white dark:bg-n0 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Image src={logo} alt="AIQuill" width={32} height={32} className="object-cover" />
              <div>
                <h1 className="text-xl font-bold text-n700 dark:text-n100 mb-1">
                  {sharedChat.title}
                </h1>
                <div className="flex items-center gap-4 text-sm text-n400">
                  <div className="flex items-center gap-1">
                    <PiClock className="text-sm" />
                    <span>Shared {formatDate(sharedChat.sharedAt)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <PiEye className="text-sm" />
                    <span>{sharedChat.viewCount} view{sharedChat.viewCount !== 1 ? 's' : ''}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {sharedChat.isPublic ? (
                      <>
                        <PiGlobe className="text-sm" />
                        <span>Public</span>
                      </>
                    ) : (
                      <>
                        <PiLock className="text-sm" />
                        <span>Private</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {isLoggedIn ? (
                <div className="flex items-center gap-3">
                  <span className="text-sm text-n500">Welcome back, {userName}!</span>
                  <button
                    onClick={handleContinueChat}
                    className="px-4 py-2 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 transition-colors flex items-center gap-2"
                  >
                    <PiChatCircle />
                    Continue Chat
                  </button>
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleLoginRedirect}
                    className="px-4 py-2 border border-primaryColor text-primaryColor rounded-lg hover:bg-primaryColor/5 transition-colors flex items-center gap-2"
                  >
                    <PiSignIn />
                    Sign In to Continue
                  </button>
                  <button
                    onClick={() => window.location.href = '/'}
                    className="px-4 py-2 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 transition-colors"
                  >
                    Try AIQuill
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="space-y-6">
          {sharedChat.messages.map((message, index) => {
            if (message.isUser) {
              return (
                <UserMessage
                  key={index}
                  message={typeof message.text === 'string' ? message.text : JSON.stringify(message.text)}
                  timestamp={message.timestamp}
                  uploadedFiles={message.uploadedFiles}
                  uploadedURLs={message.uploadedURLs}
                  selectedLanguage="English"
                />
              );
            } else {
              return (
                <BotReply
                  key={index}
                  replyType="response"
                  setScroll={() => {}} // No scroll functionality needed for shared view
                  isAnimation={false}
                  aiResponse={message.text}
                  timestamp={message.timestamp}
                  messageId={`shared-${index}`}
                  selectedLanguage="en"
                  isSharedView={true} // Add this prop to disable certain features
                />
              );
            }
          })}
        </div>

        {/* Continue Chat Section */}
        <div className="mt-12 pt-8 border-t border-primaryColor/10">
          <div className="bg-gradient-to-r from-primaryColor/5 to-blue-500/5 rounded-xl p-8 text-center">
            <div className="max-w-2xl mx-auto">
              <PiChatCircle className="text-4xl text-primaryColor mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-n700 dark:text-n100 mb-4">
                Want to continue this conversation?
              </h3>

              {isLoggedIn ? (
                <div className="space-y-4">
                  <p className="text-n500 dark:text-n300">
                    Welcome back, {userName}! You can continue this conversation and add your own messages.
                  </p>
                  <button
                    onClick={handleContinueChat}
                    className="px-8 py-3 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 transition-colors flex items-center gap-2 mx-auto text-lg font-medium"
                  >
                    <PiChatCircle />
                    Continue This Chat
                    <PiArrowRight />
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-n500 dark:text-n300">
                    Sign in to continue this conversation, ask follow-up questions, and create your own AI-powered chats.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <button
                      onClick={handleLoginRedirect}
                      className="px-8 py-3 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 transition-colors flex items-center gap-2 justify-center text-lg font-medium"
                    >
                      <PiSignIn />
                      Sign In to Continue
                      <PiArrowRight />
                    </button>
                    <button
                      onClick={() => window.location.href = '/sign-up'}
                      className="px-8 py-3 border border-primaryColor text-primaryColor rounded-lg hover:bg-primaryColor/5 transition-colors flex items-center gap-2 justify-center text-lg font-medium"
                    >
                      Create Free Account
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 pt-8 border-t border-primaryColor/10">
          <div className="text-center">
            <p className="text-n400">
              This conversation was shared from AIQuill
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SharedChatPage;
