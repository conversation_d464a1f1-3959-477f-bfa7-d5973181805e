/**
 * Upload Service for ChatInputUpload component
 * Handles file and URL uploads to the Python FAISS backend
 * Enhanced with cache memory functionality for faster responses
 */

import axios from 'axios';
import { getFaissConfig } from './faissService';

// Cache configuration
const CACHE_CONFIG = {
  UPLOAD_CACHE_KEY: 'upload_cache',
  QUERY_CACHE_KEY: 'query_cache',
  CACHE_EXPIRY_MS: 24 * 60 * 60 * 1000, // 24 hours
  MAX_CACHE_SIZE: 100, // Maximum number of cached items
  ENABLE_CACHE: true
};

// Cache interface
interface CacheItem<T> {
  data: T;
  timestamp: number;
  key: string;
  expiresAt: number;
}

interface UploadCacheData {
  success: boolean;
  message?: string;
  error?: string;
  upload_id?: string;
  index_name?: string;
  filename?: string;
  fileHash?: string;
  fileSize?: number;
  processingTime?: number;
}

// Cache utility class
class CacheManager {
  private static instance: CacheManager;

  private constructor() {}

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  // Generate cache key for uploads
  generateUploadCacheKey(type: string, data: File | string, options: any = {}): string {
    if (data instanceof File) {
      return `upload_${type}_${data.name}_${data.size}_${data.lastModified}_${options.index_name || 'default'}`;
    } else {
      return `upload_${type}_${this.hashString(data)}_${options.index_name || 'default'}`;
    }
  }

  // Generate cache key for queries
  generateQueryCacheKey(query: string, indexName: string, options: any = {}): string {
    return `query_${this.hashString(query)}_${indexName}_${JSON.stringify(options)}`;
  }

  // Simple string hash function
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  // Get cached item
  get<T>(cacheKey: string, key: string): T | null {
    if (!CACHE_CONFIG.ENABLE_CACHE || typeof window === 'undefined') return null;

    try {
      const cacheData = localStorage.getItem(cacheKey);
      if (!cacheData) return null;

      const cache: Record<string, CacheItem<T>> = JSON.parse(cacheData);
      const item = cache[key];

      if (!item) return null;

      // Check if item has expired
      if (Date.now() > item.expiresAt) {
        this.remove(cacheKey, key);
        return null;
      }

      console.log(`💾 Cache hit for key: ${key}`);
      return item.data;
    } catch (error) {
      console.error('Error reading from cache:', error);
      return null;
    }
  }

  // Set cached item
  set<T>(cacheKey: string, key: string, data: T, customExpiryMs?: number): void {
    if (!CACHE_CONFIG.ENABLE_CACHE || typeof window === 'undefined') return;

    try {
      const expiryMs = customExpiryMs || CACHE_CONFIG.CACHE_EXPIRY_MS;
      const cacheData = localStorage.getItem(cacheKey);
      let cache: Record<string, CacheItem<T>> = {};

      if (cacheData) {
        cache = JSON.parse(cacheData);
      }

      // Clean expired items and enforce size limit
      this.cleanCache(cache);

      const item: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        key,
        expiresAt: Date.now() + expiryMs
      };

      cache[key] = item;
      localStorage.setItem(cacheKey, JSON.stringify(cache));
      console.log(`💾 Cached item with key: ${key}`);
    } catch (error) {
      console.error('Error writing to cache:', error);
    }
  }

  // Remove cached item
  remove(cacheKey: string, key: string): void {
    if (typeof window === 'undefined') return;

    try {
      const cacheData = localStorage.getItem(cacheKey);
      if (!cacheData) return;

      const cache = JSON.parse(cacheData);
      delete cache[key];
      localStorage.setItem(cacheKey, JSON.stringify(cache));
    } catch (error) {
      console.error('Error removing from cache:', error);
    }
  }

  // Clean expired items and enforce size limit
  private cleanCache<T>(cache: Record<string, CacheItem<T>>): void {
    const now = Date.now();
    const keys = Object.keys(cache);

    // Remove expired items
    keys.forEach(key => {
      if (cache[key].expiresAt < now) {
        delete cache[key];
      }
    });

    // Enforce size limit by removing oldest items
    const remainingKeys = Object.keys(cache);
    if (remainingKeys.length > CACHE_CONFIG.MAX_CACHE_SIZE) {
      const sortedKeys = remainingKeys.sort((a, b) => cache[a].timestamp - cache[b].timestamp);
      const keysToRemove = sortedKeys.slice(0, remainingKeys.length - CACHE_CONFIG.MAX_CACHE_SIZE);
      keysToRemove.forEach(key => delete cache[key]);
    }
  }

  // Clear all cache
  clearCache(cacheKey: string): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(cacheKey);
    console.log(`🗑️ Cleared cache: ${cacheKey}`);
  }

  // Get cache statistics
  getCacheStats(cacheKey: string): { size: number; oldestItem: number; newestItem: number } {
    if (typeof window === 'undefined') return { size: 0, oldestItem: 0, newestItem: 0 };

    try {
      const cacheData = localStorage.getItem(cacheKey);
      if (!cacheData) return { size: 0, oldestItem: 0, newestItem: 0 };

      const cache = JSON.parse(cacheData);
      const items = Object.values(cache) as CacheItem<any>[];

      if (items.length === 0) return { size: 0, oldestItem: 0, newestItem: 0 };

      const timestamps = items.map(item => item.timestamp);
      return {
        size: items.length,
        oldestItem: Math.min(...timestamps),
        newestItem: Math.max(...timestamps)
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return { size: 0, oldestItem: 0, newestItem: 0 };
    }
  }
}

// Configure axios defaults for longer timeouts
const api = axios.create({
  timeout: 120000, // 2 minutes timeout for long-running operations
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false, // Disable credentials for CORS
});

// Initialize cache manager
const cacheManager = CacheManager.getInstance();

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log('🚀 Making request to:', config.url);
    console.log('📤 Request config:', {
      method: config.method,
      url: config.url,
      headers: config.headers,
      timeout: config.timeout
    });
    return config;
  },
  (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log('✅ Response received:', {
      status: response.status,
      statusText: response.statusText,
      url: response.config.url
    });
    return response;
  },
  (error) => {
    console.error('❌ Response interceptor error:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      data: error.response?.data
    });
    return Promise.reject(error);
  }
);

// Backend configuration - Use the same port as FAISS service for consistency
const BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5010';

// API endpoints
const ENDPOINTS = {
  PROCESS_YOUTUBE: '/api/process_youtube',
  PROCESS_ARTICLE: '/api/process_article',
  PROCESS_DOCUMENT: '/api/process_document',
  PROCESS_PDF: '/api/process_pdf',
  PROCESS_AUDIO: '/api/process_audio',
  SEARCH: '/api/search',
  HEALTH: '/api/health'
};

// Types
export interface UploadResponse {
  success: boolean;
  message?: string;
  error?: string;
  upload_id?: string;
  index_name?: string;
  filename?: string;
}

export interface SearchResponse {
  success: boolean;
  results: SearchResult[];
  query: string;
  index_name?: string;
  enhanced_response?: {
    content: string;
    model: string;
    sources_used: number;
  };
  error?: string;
  cached?: boolean;
  searchTime?: number;
}

export interface SearchResult {
  score: number;
  text: string;
  source_type: string;
  url: string;
  title: string;
  date: string;
  category: string;
  vector_id: string;
  index_name?: string;
}

export interface UploadOptions {
  index_name?: string;
  client_email?: string;
}

/**
 * Process YouTube URL with caching
 */
export async function processYouTubeURL(
  url: string,
  options: UploadOptions = {}
): Promise<UploadResponse> {
  try {
    console.log('🎥 Starting YouTube URL processing:', url);

    // Get selected index from localStorage if not provided in options
    const selectedIndex = options.index_name ||
      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||
      'default';

    console.log(`📌 Using index for YouTube processing: ${selectedIndex}`);

    // Check cache first
    const cacheKey = cacheManager.generateUploadCacheKey('youtube', url, { index_name: selectedIndex });
    const cachedResult = cacheManager.get<UploadCacheData>(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey);

    if (cachedResult) {
      console.log('💾 Using cached YouTube processing result');
      return {
        success: cachedResult.success,
        message: cachedResult.message + ' (from cache)',
        error: cachedResult.error,
        upload_id: cachedResult.upload_id,
        index_name: cachedResult.index_name,
        filename: cachedResult.filename
      };
    }

    const requestData = {
      url,
      index_name: selectedIndex,
      client_email: options.client_email || ''
    };

    console.log('📤 Sending request to:', `${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_YOUTUBE}`);
    console.log('📤 Request data:', requestData);

    const startTime = Date.now();
    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_YOUTUBE}`, requestData);
    const processingTime = Date.now() - startTime;

    console.log('✅ YouTube processing response:', response.data);

    // Cache successful results
    if (response.data.success) {
      const cacheData: UploadCacheData = {
        success: response.data.success,
        message: response.data.message,
        upload_id: response.data.upload_id,
        index_name: response.data.index_name,
        filename: response.data.filename,
        processingTime
      };

      // Cache for 24 hours for successful URL processing
      cacheManager.set(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey, cacheData);
      console.log('💾 Cached YouTube processing result');
    }

    return response.data;
  } catch (error: any) {
    console.error('❌ Error processing YouTube URL:', error);

    // Log detailed error information first
    const errorDetails = {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method
    };
    console.error('🔍 Detailed error information:', errorDetails);

    // Handle network errors
    if (error.code === 'ERR_NETWORK') {
      console.error('🌐 Network error detected - backend may be unreachable');
      return {
        success: false,
        error: `Network error - Cannot reach backend at ${BACKEND_BASE_URL}. Please check if the server is running on port 5010.`
      };
    }

    // Handle timeout specifically
    if (error.code === 'ECONNABORTED') {
      console.error('⏰ Request timeout detected');
      return {
        success: false,
        error: 'Processing timeout - the video is still being processed in the background'
      };
    }

    // Handle CORS errors
    if (error.message?.includes('CORS') || error.code === 'ERR_BLOCKED_BY_CLIENT') {
      console.error('🚫 CORS error detected');
      return {
        success: false,
        error: 'CORS error - please check backend CORS configuration'
      };
    }

    // Handle connection refused
    if (error.code === 'ECONNREFUSED') {
      console.error('🔌 Connection refused - backend server not running');
      return {
        success: false,
        error: `Connection refused - Backend server is not running on ${BACKEND_BASE_URL}`
      };
    }

    // Handle other specific error codes
    if (error.response?.status === 404) {
      console.error('🔍 Endpoint not found');
      return {
        success: false,
        error: `Endpoint not found: ${ENDPOINTS.PROCESS_YOUTUBE}`
      };
    }

    if (error.response?.status === 500) {
      console.error('💥 Server error');
      return {
        success: false,
        error: `Server error: ${error.response?.data?.error || 'Internal server error'}`
      };
    }

    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to process YouTube URL'
    };
  }
}

/**
 * Process Article URL with caching
 */
export async function processArticleURL(
  url: string,
  options: UploadOptions = {}
): Promise<UploadResponse> {
  try {
    console.log('📰 Starting Article URL processing:', url);

    // Get selected index from localStorage if not provided in options
    const selectedIndex = options.index_name ||
      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||
      'default';

    console.log(`📌 Using index for Article processing: ${selectedIndex}`);

    // Check cache first
    const cacheKey = cacheManager.generateUploadCacheKey('article', url, { index_name: selectedIndex });
    const cachedResult = cacheManager.get<UploadCacheData>(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey);

    if (cachedResult) {
      console.log('💾 Using cached Article processing result');
      return {
        success: cachedResult.success,
        message: cachedResult.message + ' (from cache)',
        error: cachedResult.error,
        upload_id: cachedResult.upload_id,
        index_name: cachedResult.index_name,
        filename: cachedResult.filename
      };
    }

    const startTime = Date.now();
    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_ARTICLE}`, {
      url,
      index_name: selectedIndex,
      client_email: options.client_email || ''
    });
    const processingTime = Date.now() - startTime;

    console.log('✅ Article processing response:', response.data);

    // Cache successful results
    if (response.data.success) {
      const cacheData: UploadCacheData = {
        success: response.data.success,
        message: response.data.message,
        upload_id: response.data.upload_id,
        index_name: response.data.index_name,
        filename: response.data.filename,
        processingTime
      };

      // Cache for 24 hours for successful URL processing
      cacheManager.set(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey, cacheData);
      console.log('💾 Cached Article processing result');
    }

    return response.data;
  } catch (error: any) {
    console.error('❌ Error processing Article URL:', error);

    // Handle timeout specifically
    if (error.code === 'ECONNABORTED') {
      return {
        success: false,
        error: 'Processing timeout - the article is still being processed in the background'
      };
    }

    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to process Article URL'
    };
  }
}

/**
 * Process Document file
 */
export async function processDocument(
  file: File,
  options: UploadOptions = {}
): Promise<UploadResponse> {
  try {
    console.log('📄 Starting Document processing:', file.name);

    // Get selected index from localStorage if not provided in options
    const selectedIndex = options.index_name ||
      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||
      'default';

    console.log(`📌 Using index for Document processing: ${selectedIndex}`);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('index_name', selectedIndex);
    formData.append('client_email', options.client_email || '');

    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_DOCUMENT}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // 3 minutes for file uploads
    });

    console.log('✅ Document processing response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('❌ Error processing Document:', error);

    // Handle timeout specifically
    if (error.code === 'ECONNABORTED') {
      return {
        success: false,
        error: 'Processing timeout - the document is still being processed in the background'
      };
    }

    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to process Document'
    };
  }
}

/**
 * Process PDF file
 */
export async function processPDF(
  file: File,
  options: UploadOptions = {}
): Promise<UploadResponse> {
  try {
    console.log('📄 Starting PDF processing:', file.name);

    // Get selected index from localStorage if not provided in options
    const selectedIndex = options.index_name ||
      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||
      'default';

    console.log(`📌 Using index for PDF processing: ${selectedIndex}`);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('index_name', selectedIndex);
    formData.append('client_email', options.client_email || '');

    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_PDF}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // 3 minutes for file uploads
    });

    console.log('✅ PDF processing response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('❌ Error processing PDF:', error);

    // Handle timeout specifically
    if (error.code === 'ECONNABORTED') {
      return {
        success: false,
        error: 'Processing timeout - the PDF is still being processed in the background'
      };
    }

    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to process PDF'
    };
  }
}

/**
 * Process Audio file
 */
export async function processAudio(
  file: File,
  options: UploadOptions = {}
): Promise<UploadResponse> {
  try {
    console.log('🎵 Starting Audio processing:', file.name);

    // Get selected index from localStorage if not provided in options
    const selectedIndex = options.index_name ||
      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||
      'default';

    console.log(`📌 Using index for Audio processing: ${selectedIndex}`);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('index_name', selectedIndex);
    formData.append('client_email', options.client_email || '');

    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_AUDIO}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 300000, // 5 minutes for audio processing (transcription takes time)
    });

    console.log('✅ Audio processing response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('❌ Error processing Audio:', error);

    // Handle timeout specifically
    if (error.code === 'ECONNABORTED') {
      return {
        success: false,
        error: 'Processing timeout - the audio is still being processed in the background'
      };
    }

    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to process Audio'
    };
  }
}

/**
 * Search with enhanced DeepSeek integration and caching
 */
export async function searchContent(
  query: string,
  options: {
    k?: number;
    index_name?: string;
    use_deepseek?: boolean;
  } = {}
): Promise<SearchResponse> {
  try {
    console.log('🔍 Starting content search:', query);

    // Check cache first for search results
    const cacheKey = cacheManager.generateQueryCacheKey(query, options.index_name || 'default', options);
    const cachedResult = cacheManager.get<SearchResponse>(CACHE_CONFIG.QUERY_CACHE_KEY, cacheKey);

    // Track cache statistics
    cacheHitStats.totalRequests++;

    if (cachedResult) {
      console.log('💾 Using cached search result');
      cacheHitStats.cacheHits++;
      return {
        ...cachedResult,
        cached: true
      };
    }

    cacheHitStats.cacheMisses++;

    const startTime = Date.now();
    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.SEARCH}`, {
      query,
      k: options.k || 5,
      index_name: options.index_name,
      use_deepseek: options.use_deepseek || false
    });
    const searchTime = Date.now() - startTime;

    console.log('✅ Search response:', response.data);

    const searchResult: SearchResponse = {
      success: true,
      searchTime,
      ...response.data
    };

    // Cache successful search results for 1 hour
    if (searchResult.success && searchResult.results && searchResult.results.length > 0) {
      cacheManager.set(CACHE_CONFIG.QUERY_CACHE_KEY, cacheKey, searchResult, 60 * 60 * 1000); // 1 hour
      console.log('💾 Cached search result');
    }

    return searchResult;
  } catch (error: any) {
    console.error('❌ Error searching content:', error);
    return {
      success: false,
      results: [],
      query,
      error: error.response?.data?.error || error.message || 'Failed to search content'
    };
  }
}

/**
 * Test basic connection to backend
 */
export async function testConnection(): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    console.log('🔌 Testing connection to backend...');
    console.log('🎯 Backend URL:', BACKEND_BASE_URL);

    // Try a simple GET request first
    const response = await axios.get(`${BACKEND_BASE_URL}/api/list-faiss-indexes`, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: false
    });

    console.log('✅ Connection test successful:', response.data);
    return {
      success: true,
      message: 'Backend connection successful'
    };
  } catch (error: any) {
    console.error('❌ Connection test failed:', error);

    // Provide detailed error information
    const errorDetails = {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url
    };

    console.error('🔍 Error details:', errorDetails);

    return {
      success: false,
      error: `Connection failed: ${error.message} (${error.code || 'Unknown error'})`
    };
  }
}

/**
 * Check backend health
 */
export async function checkBackendHealth(): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    console.log('🏥 Checking backend health...');
    const response = await api.get(`${BACKEND_BASE_URL}${ENDPOINTS.HEALTH}`, {
      timeout: 10000 // 10 seconds for health check
    });
    console.log('✅ Backend health check successful:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('❌ Backend health check failed:', error);
    return {
      success: false,
      error: error.message || 'Backend is not available'
    };
  }
}

/**
 * Get upload options with FAISS configuration
 */
function getUploadOptionsWithConfig(options: UploadOptions = {}): UploadOptions {
  const faissConfig = getFaissConfig();

  return {
    index_name: options.index_name || faissConfig?.indexName || 'default',
    client_email: options.client_email || faissConfig?.clientEmail || ''
  };
}

/**
 * Process PDF/Document file with unified handling
 */
export async function processPDFDocument(
  file: File,
  options: UploadOptions = {}
): Promise<UploadResponse> {
  try {
    console.log('📄 Starting PDF/Document processing:', file.name);

    // Validate file type
    const fileName = file.name.toLowerCase();
    const isPDF = fileName.endsWith('.pdf');
    const isDocument = fileName.endsWith('.doc') || fileName.endsWith('.docx') ||
                     fileName.endsWith('.txt') || fileName.endsWith('.rtf');

    if (!isPDF && !isDocument) {
      return {
        success: false,
        error: 'Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or RTF files.'
      };
    }

    // Get selected index from localStorage if not provided in options
    const selectedIndex = options.index_name ||
      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||
      'default';

    console.log(`📌 Using index for ${isPDF ? 'PDF' : 'Document'} processing: ${selectedIndex}`);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('index_name', selectedIndex);
    formData.append('client_email', options.client_email || '');

    // Choose the appropriate endpoint based on file type
    const endpoint = isPDF ? ENDPOINTS.PROCESS_PDF : ENDPOINTS.PROCESS_DOCUMENT;

    console.log(`📤 Sending ${isPDF ? 'PDF' : 'Document'} to endpoint:`, `${BACKEND_BASE_URL}${endpoint}`);

    const response = await api.post(`${BACKEND_BASE_URL}${endpoint}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // 3 minutes for file uploads
    });

    console.log(`✅ ${isPDF ? 'PDF' : 'Document'} processing response:`, response.data);
    return response.data;
  } catch (error: any) {
    console.error('❌ Error processing PDF/Document:', error);

    // Handle timeout specifically
    if (error.code === 'ECONNABORTED') {
      return {
        success: false,
        error: 'Processing timeout - the file is still being processed in the background'
      };
    }

    // Handle network errors
    if (error.code === 'ERR_NETWORK') {
      return {
        success: false,
        error: `Network error - Cannot reach backend at ${BACKEND_BASE_URL}. Please check if the server is running.`
      };
    }

    // Handle file size errors
    if (error.response?.status === 413) {
      return {
        success: false,
        error: 'File too large. Please upload a file smaller than 50MB.'
      };
    }

    // Handle unsupported file type errors
    if (error.response?.status === 400 && error.response?.data?.error?.includes('Unsupported file type')) {
      return {
        success: false,
        error: 'Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or RTF files.'
      };
    }

    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to process file'
    };
  }
}

/**
 * Upload handler that routes to appropriate processor based on type
 */
export async function handleUpload(
  type: 'pdf' | 'youtube' | 'article' | 'mp3',
  data: File | string,
  options: UploadOptions = {}
): Promise<UploadResponse> {
  // Merge with FAISS configuration
  const uploadOptions = getUploadOptionsWithConfig(options);

  switch (type) {
    case 'youtube':
      return processYouTubeURL(data as string, uploadOptions);
    case 'article':
      return processArticleURL(data as string, uploadOptions);
    case 'pdf':
      // Use the unified PDF/Document processor
      return processPDFDocument(data as File, uploadOptions);
    case 'mp3':
      return processAudio(data as File, uploadOptions);
    default:
      return {
        success: false,
        error: `Unsupported upload type: ${type}`
      };
  }
}

// Cache hit rate tracking
let cacheHitStats = {
  totalRequests: 0,
  cacheHits: 0,
  cacheMisses: 0
};

// Cache utility functions for external use
export const CacheUtils = {
  /**
   * Clear all upload cache
   */
  clearUploadCache: () => {
    cacheManager.clearCache(CACHE_CONFIG.UPLOAD_CACHE_KEY);
  },

  /**
   * Clear all query cache
   */
  clearQueryCache: () => {
    cacheManager.clearCache(CACHE_CONFIG.QUERY_CACHE_KEY);
  },

  /**
   * Clear all caches
   */
  clearAllCache: () => {
    cacheManager.clearCache(CACHE_CONFIG.UPLOAD_CACHE_KEY);
    cacheManager.clearCache(CACHE_CONFIG.QUERY_CACHE_KEY);
  },

  /**
   * Get cache statistics
   */
  getCacheStats: () => {
    return {
      upload: cacheManager.getCacheStats(CACHE_CONFIG.UPLOAD_CACHE_KEY),
      query: cacheManager.getCacheStats(CACHE_CONFIG.QUERY_CACHE_KEY)
    };
  },

  /**
   * Get cache hit rate statistics
   */
  getCacheHitRate: () => {
    const hitRate = cacheHitStats.totalRequests > 0
      ? (cacheHitStats.cacheHits / cacheHitStats.totalRequests * 100).toFixed(2)
      : '0.00';

    return {
      ...cacheHitStats,
      hitRate: `${hitRate}%`
    };
  },

  /**
   * Reset cache hit rate statistics
   */
  resetCacheHitStats: () => {
    cacheHitStats = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  },

  /**
   * Check if caching is enabled
   */
  isCacheEnabled: () => CACHE_CONFIG.ENABLE_CACHE,

  /**
   * Toggle cache functionality
   */
  toggleCache: (enabled: boolean) => {
    CACHE_CONFIG.ENABLE_CACHE = enabled;
    console.log(`💾 Cache ${enabled ? 'enabled' : 'disabled'}`);
  }
};
