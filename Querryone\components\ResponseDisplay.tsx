import React from 'react';
import { <PERSON>Circle, XCircle, Clock, Info, AlertTriangle } from 'lucide-react';

// Type definitions for the standardized API response format
interface ResponseMetadata {
  [key: string]: any;
}

interface ErrorDetails {
  [key: string]: any;
}

interface ApiError {
  message: string;
  type: string;
  technical_details?: string;
  details?: ErrorDetails;
}

interface StandardApiResponse {
  success: boolean;
  status: 'success' | 'error' | 'processing';
  message: string;
  timestamp: string;
  data?: any;
  metadata?: ResponseMetadata;
  error?: ApiError;
  process_id?: string;
  progress?: {
    percentage?: number;
    current_step?: string;
    total_steps?: number;
  };
}

interface ResponseDisplayProps {
  response: StandardApiResponse;
  className?: string;
}

const ResponseDisplay: React.FC<ResponseDisplayProps> = ({ response, className = '' }) => {
  const getStatusIcon = () => {
    switch (response.status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'processing':
        return <Clock className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (response.status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'processing':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch {
      return timestamp;
    }
  };

  const renderProgressBar = () => {
    if (!response.progress?.percentage) return null;
    
    return (
      <div className="mt-3">
        <div className="flex justify-between text-sm text-gray-600 mb-1">
          <span>{response.progress.current_step || 'Processing...'}</span>
          <span>{response.progress.percentage}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${response.progress.percentage}%` }}
          />
        </div>
      </div>
    );
  };

  const renderErrorDetails = () => {
    if (!response.error) return null;

    return (
      <div className="mt-4 p-3 bg-red-100 border border-red-200 rounded-md">
        <div className="flex items-start">
          <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
          <div className="flex-1">
            <p className="text-sm font-medium text-red-800">
              {response.error.message}
            </p>
            
            {response.error.type && (
              <p className="text-xs text-red-600 mt-1">
                Error Type: {response.error.type}
              </p>
            )}
            
            {response.error.technical_details && (
              <details className="mt-2">
                <summary className="text-xs text-red-600 cursor-pointer hover:text-red-800">
                  Technical Details
                </summary>
                <p className="text-xs text-red-700 mt-1 font-mono bg-red-50 p-2 rounded">
                  {response.error.technical_details}
                </p>
              </details>
            )}
            
            {response.error.details && (
              <details className="mt-2">
                <summary className="text-xs text-red-600 cursor-pointer hover:text-red-800">
                  Additional Information
                </summary>
                <pre className="text-xs text-red-700 mt-1 font-mono bg-red-50 p-2 rounded overflow-x-auto">
                  {JSON.stringify(response.error.details, null, 2)}
                </pre>
              </details>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderMetadata = () => {
    if (!response.metadata) return null;

    return (
      <details className="mt-4">
        <summary className="text-sm text-gray-600 cursor-pointer hover:text-gray-800 font-medium">
          Additional Information
        </summary>
        <div className="mt-2 p-3 bg-gray-100 border border-gray-200 rounded-md">
          <pre className="text-xs text-gray-700 font-mono overflow-x-auto">
            {JSON.stringify(response.metadata, null, 2)}
          </pre>
        </div>
      </details>
    );
  };

  const renderDataPreview = () => {
    if (!response.data) return null;

    // Handle different data types
    if (Array.isArray(response.data)) {
      return (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">
            Results ({response.data.length} items)
          </h4>
          <div className="max-h-40 overflow-y-auto bg-gray-50 border border-gray-200 rounded-md p-3">
            {response.data.slice(0, 5).map((item, index) => (
              <div key={index} className="text-sm text-gray-600 mb-1">
                {typeof item === 'object' ? JSON.stringify(item) : String(item)}
              </div>
            ))}
            {response.data.length > 5 && (
              <p className="text-xs text-gray-500 italic">
                ... and {response.data.length - 5} more items
              </p>
            )}
          </div>
        </div>
      );
    }

    if (typeof response.data === 'object') {
      return (
        <details className="mt-4">
          <summary className="text-sm text-gray-600 cursor-pointer hover:text-gray-800 font-medium">
            Response Data
          </summary>
          <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md">
            <pre className="text-xs text-gray-700 font-mono overflow-x-auto">
              {JSON.stringify(response.data, null, 2)}
            </pre>
          </div>
        </details>
      );
    }

    return (
      <div className="mt-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Response Data</h4>
        <p className="text-sm text-gray-600 bg-gray-50 border border-gray-200 rounded-md p-3">
          {String(response.data)}
        </p>
      </div>
    );
  };

  return (
    <div className={`border rounded-lg p-4 ${getStatusColor()} ${className}`}>
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <div>
            <h3 className="text-sm font-medium text-gray-900">
              {response.message}
            </h3>
            <p className="text-xs text-gray-500">
              {formatTimestamp(response.timestamp)}
            </p>
          </div>
        </div>
        
        {response.process_id && (
          <span className="text-xs text-gray-500 font-mono bg-white px-2 py-1 rounded">
            ID: {response.process_id.slice(0, 8)}...
          </span>
        )}
      </div>

      {/* Progress Bar for Processing Status */}
      {response.status === 'processing' && renderProgressBar()}

      {/* Error Details */}
      {response.status === 'error' && renderErrorDetails()}

      {/* Data Preview */}
      {response.status === 'success' && renderDataPreview()}

      {/* Metadata */}
      {renderMetadata()}
    </div>
  );
};

export default ResponseDisplay;
