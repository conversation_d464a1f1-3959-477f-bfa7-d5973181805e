"use client";

import React, { Component, ReactNode } from 'react';

interface HydrationBoundaryState {
  hasHydrationError: boolean;
  error?: Error;
}

interface HydrationBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * HydrationBoundary catches hydration errors and provides a fallback UI
 * This prevents the entire app from crashing due to hydration mismatches
 */
class HydrationBoundary extends Component<HydrationBoundaryProps, HydrationBoundaryState> {
  constructor(props: HydrationBoundaryProps) {
    super(props);
    this.state = { hasHydrationError: false };
  }

  static getDerivedStateFromError(error: Error): HydrationBoundaryState {
    // Check if this is a hydration error
    const isHydrationError = 
      error.message.includes('Hydration') ||
      error.message.includes('hydration') ||
      error.message.includes('server rendered HTML') ||
      error.message.includes('client-side rendered HTML');

    if (isHydrationError) {
      console.warn('Hydration error caught by boundary:', error.message);
      return { hasHydrationError: true, error };
    }

    // Re-throw non-hydration errors
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log hydration errors for debugging
    if (this.state.hasHydrationError) {
      console.warn('Hydration error details:', {
        error: error.message,
        componentStack: errorInfo.componentStack,
      });
    }
  }

  render() {
    if (this.state.hasHydrationError) {
      // Render fallback UI for hydration errors
      return this.props.fallback || (
        <div className="hydration-error-fallback">
          {/* Render children without hydration to recover */}
          {this.props.children}
        </div>
      );
    }

    return this.props.children;
  }
}

export default HydrationBoundary;
