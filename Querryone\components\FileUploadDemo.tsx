'use client';

import React, { useState } from 'react';
import FileUploadWithFaiss from './FileUploadWithFaiss';

const FileUploadDemo: React.FC = () => {
  const [selectedLanguage, setSelectedLanguage] = useState('English');
  const [indexName, setIndexName] = useState('demo_index');
  const [clientEmail, setClientEmail] = useState('<EMAIL>');
  const [uploadResults, setUploadResults] = useState<any[]>([]);

  const languages = ['English', 'Tamil', 'Telugu', 'Kannada'];

  const handleFaissUploadSuccess = (response: any) => {
    console.log('FAISS Upload Success:', response);
    setUploadResults(prev => [...prev, {
      type: 'success',
      message: `Successfully uploaded to FAISS index: ${indexName}`,
      response,
      timestamp: new Date().toLocaleString()
    }]);
  };

  const handleFaissUploadError = (error: string) => {
    console.error('FAISS Upload Error:', error);
    setUploadResults(prev => [...prev, {
      type: 'error',
      message: `Upload failed: ${error}`,
      timestamp: new Date().toLocaleString()
    }]);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-900 min-h-screen">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          FileUploadWithFaiss Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          This demo showcases the new FileUploadWithFaiss component with integrated FAISS upload functionality.
        </p>
      </div>

      {/* Configuration Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Language
          </label>
          <select
            value={selectedLanguage}
            onChange={(e) => setSelectedLanguage(e.target.value)}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            {languages.map(lang => (
              <option key={lang} value={lang}>{lang}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Index Name
          </label>
          <input
            type="text"
            value={indexName}
            onChange={(e) => setIndexName(e.target.value)}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            placeholder="Enter index name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Client Email
          </label>
          <input
            type="email"
            value={clientEmail}
            onChange={(e) => setClientEmail(e.target.value)}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            placeholder="Enter client email"
          />
        </div>
      </div>

      {/* File Upload Component */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Upload CSV or Excel File to FAISS
        </h2>
        <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <FileUploadWithFaiss
            selectedLanguage={selectedLanguage}
            showFaissUpload={true}
            indexName={indexName}
            clientEmail={clientEmail}
            maxFileSize={50}
            allowedTypes={['text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']}
            updateMode="update"
            embedModel="all-MiniLM-L6-v2"
            onFileUpload={(files) => {
              console.log('Files selected:', files);
            }}
            onFaissUploadSuccess={handleFaissUploadSuccess}
            onFaissUploadError={handleFaissUploadError}
          />
        </div>
      </div>

      {/* Results Section */}
      {uploadResults.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Upload Results
          </h2>
          <div className="space-y-3">
            {uploadResults.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${
                  result.type === 'success'
                    ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                    : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className={`font-medium ${
                      result.type === 'success'
                        ? 'text-green-800 dark:text-green-300'
                        : 'text-red-800 dark:text-red-300'
                    }`}>
                      {result.message}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      {result.timestamp}
                    </p>
                    {result.response && (
                      <details className="mt-2">
                        <summary className="cursor-pointer text-sm text-gray-600 dark:text-gray-400">
                          View Response Details
                        </summary>
                        <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto">
                          {JSON.stringify(result.response, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Features Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Component Features
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
              Multi-language Support
            </h3>
            <p className="text-sm text-blue-600 dark:text-blue-400">
              Supports English, Tamil, Telugu, and Kannada languages with proper text localization.
            </p>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
            <h3 className="font-medium text-green-800 dark:text-green-300 mb-2">
              FAISS Integration
            </h3>
            <p className="text-sm text-green-600 dark:text-green-400">
              Direct upload to FAISS vector database with progress tracking and error handling.
            </p>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
            <h3 className="font-medium text-purple-800 dark:text-purple-300 mb-2">
              Drag & Drop
            </h3>
            <p className="text-sm text-purple-600 dark:text-purple-400">
              Intuitive drag-and-drop interface with file validation and visual feedback.
            </p>
          </div>

          <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
            <h3 className="font-medium text-orange-800 dark:text-orange-300 mb-2">
              Progress Tracking
            </h3>
            <p className="text-sm text-orange-600 dark:text-orange-400">
              Real-time upload progress with status indicators and success/error feedback.
            </p>
          </div>
        </div>
      </div>

      {/* Usage Example */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Usage Example
        </h2>
        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
          <pre className="text-sm text-gray-800 dark:text-gray-200 overflow-auto">
{`<FileUploadWithFaiss
  selectedLanguage="Tamil"
  showFaissUpload={true}
  indexName="my_data_index"
  clientEmail="<EMAIL>"
  maxFileSize={50}
  allowedTypes={['text/csv']}
  updateMode="update"
  embedModel="all-MiniLM-L6-v2"
  onFileUpload={(files) => console.log('Files:', files)}
  onFaissUploadSuccess={(response) => console.log('Success:', response)}
  onFaissUploadError={(error) => console.error('Error:', error)}
/>`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default FileUploadDemo;
