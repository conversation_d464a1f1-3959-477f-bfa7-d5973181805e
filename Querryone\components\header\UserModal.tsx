"use client";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import {
  PiPencilLine,
  PiSignOut,

} from "react-icons/pi";
import { PiUserCircle } from 'react-icons/pi';

import useModalOpen from "@/hooks/useModalOpen";
import { useMainModal } from "@/stores/modal";
import { baseUrl, uid } from "@/components/api/api";
import { useRouter } from "next/navigation";

type User = {
  name: string;
  mobileno: string;
};

// Reuse your fetchUserLogo function here
export async function fetchUserLogo(username: string, mobileno: string): Promise<string | null> {
  if (!username || !mobileno) return null;

  const apiUrl = `${baseUrl}/eRetrieve?filtercount=2&f1_field=phonenumber_S&f1_op=eq&f1_value=${mobileno}&f2_field=name_S&f2_op=eq&f2_value=${username}`;

  try {
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        xxxid: uid,
      },
    });

    if (!response.ok) throw new Error("Failed to fetch user logo");

    const contentType = response.headers.get("content-type") || "";

    if (contentType.includes("image")) {
      const blob = await response.blob();
      return URL.createObjectURL(blob);
    } else {
      const data = await response.json();
      return data.logoUrl || null;
    }
  } catch (error) {
    console.error("Error fetching user logo:", error);
    return null;
  }
};


function UserModal() {
  const { modalOpen } = useMainModal();
  const { modal, setModal, modalRef } = useModalOpen();
  const router = useRouter();

  const [profileImg, setProfileImg] = useState<string | null>(null);
  const [userName, setUserName] = useState<string>("User");
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);

  useEffect(() => {
    const storedData = sessionStorage.getItem("resultUser");
    if (!storedData) return;

    try {
      const user: User = JSON.parse(storedData);
      setUserName(user.name || "User");

      if (user.name && user.mobileno) {
        fetchUserLogo(user.name, user.mobileno).then((imgUrl) => {
          if (imgUrl && typeof imgUrl === "string") {
            setProfileImg(imgUrl);
          } else {
            // fallback: use default image
            setProfileImg(null); // just don't set it if invalid
          }
        });
      }
    } catch (error) {
      console.error("Failed to parse session user data:", error);
    }
  }, []);



  useEffect(() => {
    const handleImageUpdate = (e: Event) => {
      const customEvent = e as CustomEvent<string>;
      setProfileImg(customEvent.detail); // update state with new image
    };

    window.addEventListener("profileImageUpdated", handleImageUpdate);

    return () => {
      window.removeEventListener("profileImageUpdated", handleImageUpdate);
    };
  }, []);

  // Handle logout confirmation
  const handleLogout = () => {
    try {
      // Clear session storage
      sessionStorage.removeItem("resultUser");

      // Clear all user-related localStorage items
      localStorage.removeItem("user_email");
      localStorage.removeItem("userEmail"); // Keep this for backward compatibility
      localStorage.removeItem("pinecone_api_key");
      localStorage.removeItem("pinecone_index_name");
      localStorage.removeItem("pineconeApiKeys");
      localStorage.removeItem("userPineconeIndexes");
      localStorage.removeItem("use_dev_environment");
      localStorage.removeItem("redirectAfterLogin");
      localStorage.removeItem("faiss_index_name");
      localStorage.removeItem("faiss_embed_model");
      localStorage.removeItem("faiss_client_email");
      localStorage.removeItem("selectedFaissIndex");

      console.log("✅ Successfully cleared all user data from storage");

      // Close modals
      setModal(false);
      setShowLogoutConfirm(false);

      // Redirect to sign-in page
      router.push("/sign-in");
    } catch (error) {
      console.error("❌ Error during logout:", error);

      // Still try to redirect even if clearing storage fails
      setModal(false);
      setShowLogoutConfirm(false);
      router.push("/sign-in");
    }
  };

  // Handle logout click
  const handleLogoutClick = () => {
    setShowLogoutConfirm(true);
  };

  // fallback user image url
  const defaultProfileImg = "/images/logodefault.png";

  return (
    <div className="relative size-9" ref={modalRef}>
      <button onClick={() => setModal((prev) => !prev)}>
        <img
          src={profileImg || defaultProfileImg}
          onError={(e) => {
            e.currentTarget.src = defaultProfileImg;
          }}
          alt=""
          className="rounded-full object-cover w-full h-full"
          loading="lazy"
          decoding="async"
        />
      </button>

      <div
        className={`absolute top-12 right-0 bg-white dark:bg-n0 border border-primaryColor/30 p-3 rounded-xl text-sm duration-300 z-[100] text-n500 dark:text-n30 w-[240px] shadow-lg ${modal ? "visible translate-y-0 opacity-100" : "invisible translate-y-2 opacity-0"
          }`}
      >
        <ul className="flex flex-col gap-1 justify-start items-start">
          <li className="flex justify-start items-center gap-2 p-3 border-b border-primaryColor/30 cursor-pointer w-full">
            <img
              src={profileImg || defaultProfileImg}
              onError={(e) => {
                e.currentTarget.src = defaultProfileImg;
              }}
              alt=""
              className="size-7 rounded-full"
              loading="lazy"
              decoding="async"
            />
            <span>{userName}</span>
          </li>

          <li
            className="flex justify-start items-center gap-2 p-3 rounded-lg border border-transparent hover:border-primaryColor/30 hover:bg-primaryColor/5 duration-300 cursor-pointer w-full"
            onClick={() => {
              modalOpen("Profile");
              setModal(false);
            }}
          >
<PiUserCircle className="text-xl" />
<span>Profile</span>
          </li>


          <li
            className="flex justify-start items-center gap-2 p-3 rounded-lg border border-transparent hover:border-primaryColor/30 hover:bg-primaryColor/5 duration-300 cursor-pointer w-full"
            onClick={() => {
              modalOpen("Change Password");
              setModal(false);
            }}
          >
            <PiPencilLine className="text-xl" />
            <span>Change Password</span>
          </li>


          <li className="w-full">
            <button
              onClick={handleLogoutClick}
              className="flex justify-start items-center gap-1 p-2 rounded-lg border border-transparent hover:border-errorColor/30 hover:bg-errorColor/5 duration-300 cursor-pointer w-full text-errorColor"
            >
              <PiSignOut className="text-xl" />
              <span>Log Out</span>
            </button>
          </li>

        </ul>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/60 backdrop-blur-sm">
          <div className="bg-white dark:bg-n0 p-6 rounded-xl shadow-2xl max-w-sm w-full mx-4 border border-gray-200 dark:border-n800 transform transition-all duration-200 scale-100">
            <h2 className="text-lg font-semibold text-n800 dark:text-n10 mb-4">
              Confirm Logout
            </h2>
            <p className="text-sm text-n600 dark:text-n40 mb-6">
              Are you sure you want to log out? You will need to sign in again to access your account.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowLogoutConfirm(false)}
                className="px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n800 rounded-md hover:bg-gray-300 dark:hover:bg-n700 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={handleLogout}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors flex items-center focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                <PiSignOut className="mr-2" />
                Log Out
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default UserModal;
