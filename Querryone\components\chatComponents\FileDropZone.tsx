import React, { useState, useRef, useCallback } from 'react';
import { PiCloudArrowUp, PiFile, PiX, PiCheck } from 'react-icons/pi';

interface FileDropZoneProps {
  acceptedTypes: string;
  onFilesSelected: (files: File[]) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  selectedLanguage: string;
}

interface FileWithStatus {
  file: File;
  status: 'selected' | 'uploading' | 'success' | 'error';
  error?: string | null;
}

const FileDropZone: React.FC<FileDropZoneProps> = ({
  acceptedTypes,
  onFilesSelected,
  maxFiles = 1,
  maxFileSize = 50,
  selectedLanguage
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getLanguageText = () => {
    switch (selectedLanguage) {
      case 'Tamil':
        return {
          dragText: 'கோப்புகளை இங்கே இழுத்து விடவும் அல்லது உலாவ கிளிக் செய்யவும்',
          browseText: 'உலாவு',
          maxSizeText: `அதிகபட்சம் ${maxFileSize}MB`,
          removeText: 'அகற்று',
          selectedText: 'தேர்ந்தெடுக்கப்பட்டது'
        };
      case 'Telugu':
        return {
          dragText: 'ఫైల్‌లను ఇక్కడ లాగండి లేదా బ్రౌజ్ చేయడానికి క్లిక్ చేయండి',
          browseText: 'బ్రౌజ్',
          maxSizeText: `గరిష్టంగా ${maxFileSize}MB`,
          removeText: 'తొలగించు',
          selectedText: 'ఎంచుకోబడింది'
        };
      case 'Kannada':
        return {
          dragText: 'ಫೈಲ್‌ಗಳನ್ನು ಇಲ್ಲಿ ಎಳೆಯಿರಿ ಅಥವಾ ಬ್ರೌಸ್ ಮಾಡಲು ಕ್ಲಿಕ್ ಮಾಡಿ',
          browseText: 'ಬ್ರೌಸ್',
          maxSizeText: `ಗರಿಷ್ಠ ${maxFileSize}MB`,
          removeText: 'ತೆಗೆದುಹಾಕಿ',
          selectedText: 'ಆಯ್ಕೆಮಾಡಲಾಗಿದೆ'
        };
      default:
        return {
          dragText: 'Drag files here or click to browse',
          browseText: 'Browse',
          maxSizeText: `Max ${maxFileSize}MB`,
          removeText: 'Remove',
          selectedText: 'Selected'
        };
    }
  };

  const getBorderColor = () => {
    switch (selectedLanguage) {
      case 'Tamil':
        return isDragging ? 'border-purple-400 bg-purple-50' : 'border-purple-300';
      case 'Telugu':
        return isDragging ? 'border-green-400 bg-green-50' : 'border-green-300';
      case 'Kannada':
        return isDragging ? 'border-orange-400 bg-orange-50' : 'border-orange-300';
      default:
        return isDragging ? 'border-blue-400 bg-blue-50' : 'border-blue-300';
    }
  };

  const getIconColor = () => {
    switch (selectedLanguage) {
      case 'Tamil':
        return 'text-purple-500';
      case 'Telugu':
        return 'text-green-500';
      case 'Kannada':
        return 'text-orange-500';
      default:
        return 'text-blue-500';
    }
  };

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size exceeds ${maxFileSize}MB limit`;
    }

    // Check file type
    const fileName = file.name.toLowerCase();
    const acceptedExtensions = acceptedTypes.split(',').map(type => type.trim().toLowerCase());

    // More comprehensive file type validation
    const isValidType = acceptedExtensions.some(acceptedType => {
      if (acceptedType.startsWith('.')) {
        return fileName.endsWith(acceptedType);
      }
      return file.type === acceptedType;
    });

    if (!isValidType) {
      // Provide more specific error messages based on accepted types
      let typeDescription = '';
      if (acceptedTypes.includes('.pdf')) {
        typeDescription = 'PDF, DOC, DOCX, TXT, or RTF files';
      } else if (acceptedTypes.includes('.mp3')) {
        typeDescription = 'MP3, WAV, M4A, or FLAC audio files';
      } else {
        typeDescription = acceptedTypes;
      }

      return `File type not supported. Please upload ${typeDescription}`;
    }

    return null;
  };

  const handleFiles = useCallback((fileList: FileList) => {
    const newFiles: FileWithStatus[] = [];
    const validFiles: File[] = [];

    Array.from(fileList).slice(0, maxFiles).forEach(file => {
      const error = validateFile(file);
      const fileWithStatus: FileWithStatus = {
        file,
        status: error ? 'error' : 'selected',
        error
      };
      
      newFiles.push(fileWithStatus);
      
      if (!error) {
        validFiles.push(file);
      }
    });

    setFiles(newFiles);
    
    if (validFiles.length > 0) {
      onFilesSelected(validFiles);
    }
  }, [maxFiles, onFilesSelected]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  const handleRemoveFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    setFiles(newFiles);
    
    const validFiles = newFiles
      .filter(f => f.status !== 'error')
      .map(f => f.file);
    
    onFilesSelected(validFiles);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const text = getLanguageText();

  return (
    <div className="space-y-4">
      {/* Drop zone */}
      <div
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`
          border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200
          ${getBorderColor()}
          hover:bg-gray-50 dark:hover:bg-gray-700/50
          min-h-[160px] flex items-center justify-center
        `}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedTypes}
          multiple={maxFiles > 1}
          onChange={handleFileInputChange}
          className="hidden"
        />

        <div className="flex flex-col items-center justify-center gap-3">
          <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-800">
            <PiCloudArrowUp className={`w-10 h-10 ${getIconColor()}`} />
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-relaxed">
              {text.dragText}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {text.maxSizeText}
            </p>
          </div>
        </div>
      </div>

      {/* Selected files */}
      {files.length > 0 && (
        <div className="space-y-3">
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              {text.selectedText} ({files.length})
            </h4>
            <div className="space-y-2">
              {files.map((fileWithStatus, index) => (
                <div
                  key={index}
                  className={`
                    flex items-center gap-4 p-4 rounded-lg border transition-all duration-200
                    ${fileWithStatus.status === 'error'
                      ? 'border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800'
                      : 'border-gray-200 bg-gray-50 dark:bg-gray-700/50 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }
                  `}
                >
                  <div className="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600">
                    {fileWithStatus.status === 'error' ? (
                      <PiX className="w-5 h-5 text-red-500" />
                    ) : fileWithStatus.status === 'success' ? (
                      <PiCheck className="w-5 h-5 text-green-500" />
                    ) : (
                      <PiFile className={`w-5 h-5 ${getIconColor()}`} />
                    )}
                  </div>

                  <div className="flex-grow min-w-0 space-y-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {fileWithStatus.file.name}
                    </p>
                    <div className="flex items-center gap-2">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {(fileWithStatus.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      {fileWithStatus.status === 'selected' && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                          Ready
                        </span>
                      )}
                    </div>
                    {fileWithStatus.error && (
                      <p className="text-xs text-red-600 dark:text-red-400 mt-1 leading-relaxed">
                        {fileWithStatus.error}
                      </p>
                    )}
                  </div>

                  <button
                    onClick={() => handleRemoveFile(index)}
                    className="flex-shrink-0 p-2 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200"
                    title={text.removeText}
                  >
                    <PiX className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileDropZone;
