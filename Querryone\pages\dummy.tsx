import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { PiArrowLeft, PiArrowSquareOut, PiTranslate } from 'react-icons/pi';

type Language = 'english' | 'tamil' | 'telugu' | 'kannada';

interface Labels {
  reference: string;
  source: string;
  visit: string;
  back: string;
  translate: string;
  translating: string;
  originalText: string;
  translatedText: string;
}

const getLabels = (language: Language): Labels => {
  switch (language) {
    case 'tamil':
      return {
        reference: 'மேற்கோள்',
        source: 'மூல இணைப்பு',
        visit: 'பார்வையிடு',
        back: 'திரும்பிச் செல்',
        translate: 'மொழிபெயர்',
        translating: 'மொழிபெயர்க்கிறது...',
        originalText: 'அசல் உரை',
        translatedText: 'மொழிபெயர்க்கப்பட்ட உரை'
      };
    case 'telugu':
      return {
        reference: 'సూచన',
        source: 'మూల లింక్',
        visit: 'సందర్శించండి',
        back: 'వెనుకకు',
        translate: 'అనువదించు',
        translating: 'అనువదిస్తోంది...',
        originalText: 'అసలు వచనం',
        translatedText: 'అనువదించబడిన వచనం'
      };
    case 'kannada':
      return {
        reference: 'ಉಲ್ಲೇಖ',
        source: 'ಮೂಲ ಲಿಂಕ್',
        visit: 'ಭೇಟಿ ನೀಡಿ',
        back: 'ಹಿಂದೆ',
        translate: 'ಅನುವಾದಿಸು',
        translating: 'ಅನುವಾದಿಸಲಾಗುತ್ತಿದೆ...',
        originalText: 'ಮೂಲ ಪಠ್ಯ',
        translatedText: 'ಅನುವಾದಿತ ಪಠ್ಯ'
      };
    default:
      return {
        reference: 'Reference',
        source: 'Source Link',
        visit: 'Visit',
        back: 'Back',
        translate: 'Translate',
        translating: 'Translating...',
        originalText: 'Original Text',
        translatedText: 'Translated Text'
      };
  }
};

const DummyPage: React.FC = () => {
  const router = useRouter();
  const { url, domain, referenceNumber, language, returnUrl } = router.query;
  const [isTranslating, setIsTranslating] = useState(false);
  const [translatedContent, setTranslatedContent] = useState<string | null>(null);
  const [pageContent, setPageContent] = useState<string | null>(null);

  const labels = getLabels(language as Language);

  useEffect(() => {
    // Fetch page content when URL is available
    const fetchPageContent = async () => {
      if (url) {
        try {
          const response = await fetch(url as string);
          const text = await response.text();
          setPageContent(text);
        } catch (error) {
          console.error('Error fetching page content:', error);
          setPageContent('Error loading content. Please try again later.');
        }
      }
    };

    fetchPageContent();
  }, [url]);

  const handleTranslate = async () => {
    if (!pageContent || isTranslating) return;

    setIsTranslating(true);
    try {
      // Simulate translation delay (replace with actual translation API call)
      await new Promise(resolve => setTimeout(resolve, 1500));
      setTranslatedContent('Translated content would appear here. This is a placeholder for the actual translation API integration.');
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  const handleBack = () => {
    if (returnUrl) {
      router.push(returnUrl as string);
    } else {
      router.back();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Back button */}
        <button
          onClick={handleBack}
          className="mb-6 flex items-center gap-2 text-primaryColor hover:text-primaryColor/80 transition-colors"
        >
          <PiArrowLeft className="h-5 w-5" />
          <span>{labels.back}</span>
        </button>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
          <div className="mb-8 pb-4 border-b border-gray-200 dark:border-gray-700">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {labels.reference} #{referenceNumber}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">{domain}</p>
          </div>
          
          <div className="space-y-6">
            {/* Source URL section */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {labels.source}
              </h2>
              <div className="flex items-center space-x-4">
                <p className="text-primaryColor break-all">
                  {url}
                </p>
                <button
                  onClick={() => window.open(url as string, '_blank')}
                  className="px-4 py-2 bg-primaryColor text-white rounded-md hover:bg-primaryColor/90 transition-colors duration-200 text-sm flex items-center gap-2"
                >
                  <span>{labels.visit}</span>
                  <PiArrowSquareOut className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Content preview section */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {labels.originalText}
                </h2>
                <button
                  onClick={handleTranslate}
                  disabled={isTranslating || !pageContent}
                  className={`px-4 py-2 rounded-md text-sm flex items-center gap-2 transition-colors duration-200
                    ${isTranslating
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-primaryColor text-white hover:bg-primaryColor/90'
                    }`}
                >
                  {isTranslating ? (
                    <>
                      <span>{labels.translating}</span>
                      <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                    </>
                  ) : (
                    <>
                      <span>{labels.translate}</span>
                      <PiTranslate className="h-4 w-4" />
                    </>
                  )}
                </button>
              </div>

              {/* Original content */}
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-4 max-h-[400px] overflow-y-auto">
                {pageContent ? (
                  <div className="prose dark:prose-invert max-w-none">
                    {pageContent}
                  </div>
                ) : (
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-5/6"></div>
                  </div>
                )}
              </div>

              {/* Translated content */}
              {translatedContent && (
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {labels.translatedText}
                  </h2>
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 max-h-[400px] overflow-y-auto">
                    <div className="prose dark:prose-invert max-w-none">
                      {translatedContent}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DummyPage; 